@php
    EcommerceHelper::registerThemeAssets();
    $version = get_cms_version();
    Theme::asset()->add('lightgallery-css', 'vendor/core/plugins/ecommerce/libraries/lightgallery/css/lightgallery.min.css', version: $version);
    Theme::asset()->container('footer')->add('lightgallery-js', 'vendor/core/plugins/ecommerce/libraries/lightgallery/js/lightgallery.min.js', ['jquery'], version: $version);

    $galleryStyle = theme_option('ecommerce_product_gallery_image_style', 'vertical');
    $totalImages = count($productImages);
    $totalVideos = 0;
    if (!empty($product->video)) {
        foreach($product->video as $video) {
            if ($video['url']) {
                $totalVideos++;
            }
        }
    }
    $totalMedia = $totalImages + $totalVideos;
@endphp

<style>
    /* Custom Image Gallery - No Slick, No Stacking */
    .custom-product-gallery {
        position: relative;
        width: 100%;
    }

    .custom-gallery-main {
        position: relative;
        width: 100%;
        height: auto;
        min-height: 300px;
        overflow: hidden;
        background: #fff;
        border-radius: 8px;
    }

    .custom-gallery-item {
        display: none;
        position: relative;
        width: 100%;
        text-align: center;
        line-height: 0;
    }

    .custom-gallery-item.active {
        display: block;
    }

    .custom-gallery-item img {
        width: 100%;
        height: auto;
        max-width: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
        cursor: zoom-in;
    }

    /* Amazon-style zoom effect for desktop */
    @media (min-width: 992px) {
        .custom-gallery-item {
            border-radius: 8px;
            position: relative;
        }

        .custom-gallery-item img {
            height: 500px;
            object-fit: contain;
            background: #f8f9fa;
            cursor: crosshair;
        }

        .custom-gallery-main {
            min-height: 500px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
        }

        /* Zoom lens overlay */
        .zoom-lens {
            position: absolute;
            border: 2px solid #007bff;
            background: rgba(255,255,255,0.3);
            width: 150px;
            height: 150px;
            display: none;
            pointer-events: none;
            z-index: 15;
            border-radius: 4px;
        }

        /* Zoomed image container */
        .zoom-result {
            position: absolute;
            left: 110%;
            top: 0;
            width: 400px;
            height: 500px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            display: none;
            z-index: 20;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        /* Adjust zoom result position if not enough space on right */
        @media (max-width: 1400px) {
            .zoom-result {
                left: -410px;
            }
        }

        .zoom-result img {
            position: absolute;
            width: auto;
            height: auto;
        }

        /* Zoom indicator */
        .zoom-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            display: none;
            z-index: 10;
        }

        .custom-gallery-item:hover .zoom-indicator {
            display: block;
        }
    }

    /* Mobile adjustments */
    @media (max-width: 991px) {
        .custom-gallery-main {
            min-height: 300px;
        }

        .custom-gallery-item img {
            height: 300px;
            object-fit: contain;
            background: #f8f9fa;
        }
    }
    
    .custom-gallery-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255,255,255,0.9);
        color: #333;
        border: 1px solid rgba(0,0,0,0.1);
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        opacity: 0;
        visibility: hidden;
    }

    .custom-gallery-main:hover .custom-gallery-nav {
        opacity: 1;
        visibility: visible;
    }

    .custom-gallery-nav:hover {
        background: rgba(255,255,255,1);
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .custom-gallery-prev {
        left: 20px;
    }

    .custom-gallery-next {
        right: 20px;
    }

    /* Show navigation on mobile always */
    @media (max-width: 991px) {
        .custom-gallery-nav {
            opacity: 1;
            visibility: visible;
            background: rgba(0,0,0,0.6);
            color: white;
            width: 40px;
            height: 40px;
            font-size: 16px;
        }

        .custom-gallery-nav:hover {
            background: rgba(0,0,0,0.8);
        }

        .custom-gallery-prev {
            left: 10px;
        }

        .custom-gallery-next {
            right: 10px;
        }
    }
    
    .custom-gallery-counter {
        position: absolute;
        bottom: 20px;
        right: 20px;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        z-index: 10;
        font-weight: 500;
    }

    .custom-gallery-thumbnails {
        display: flex;
        gap: 8px;
        margin-top: 20px;
        overflow-x: auto;
        padding: 10px 0;
        scrollbar-width: thin;
    }

    .custom-gallery-thumbnails::-webkit-scrollbar {
        height: 4px;
    }

    .custom-gallery-thumbnails::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .custom-gallery-thumbnails::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    .custom-gallery-thumb {
        flex-shrink: 0;
        width: 70px;
        height: 70px;
        border: 2px solid transparent;
        border-radius: 6px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .custom-gallery-thumb.active {
        border-color: #007bff;
        box-shadow: 0 0 0 1px rgba(0,123,255,0.25);
    }

    .custom-gallery-thumb:hover {
        border-color: #0056b3;
        transform: scale(1.05);
    }

    .custom-gallery-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.2s ease;
    }

    .custom-gallery-thumb:hover img {
        transform: scale(1.1);
    }

    /* Desktop thumbnail adjustments */
    @media (min-width: 992px) {
        .custom-gallery-thumbnails {
            gap: 12px;
            margin-top: 25px;
        }

        .custom-gallery-thumb {
            width: 90px;
            height: 90px;
        }
    }
    
    .custom-video-item {
        position: relative;
    }
    
    .custom-video-play {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.7);
        color: white;
        border: none;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        font-size: 24px;
        cursor: pointer;
        z-index: 5;
    }
</style>

<div class="custom-product-gallery">
    <div class="custom-gallery-main">
        @php $itemIndex = 0; @endphp
        
        {{-- Videos --}}
        @if (!empty($product->video))
            @foreach($product->video as $video)
                @continue(!$video['url'])
                <div class="custom-gallery-item custom-video-item {{ $itemIndex === 0 ? 'active' : '' }}" data-index="{{ $itemIndex }}">
                    @if ($video['provider'] === 'video')
                        <video controls style="max-width: 100%; max-height: 100%;">
                            <source src="{{ $video['url'] }}" type="video/mp4">
                        </video>
                    @else
                        <iframe 
                            src="{{ $video['url'] }}" 
                            style="width: 100%; height: 100%; border: none;"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    @endif
                </div>
                @php $itemIndex++; @endphp
            @endforeach
        @endif
        
        {{-- Images --}}
        @foreach ($productImages as $image)
            <a href="{{ RvMedia::getImageUrl($image) }}" class="custom-gallery-item {{ $itemIndex === 0 ? 'active' : '' }}" data-index="{{ $itemIndex }}">
                {{ RvMedia::image($image, $product->name) }}
                <div class="zoom-lens"></div>
                <div class="zoom-result">
                    <img src="{{ RvMedia::getImageUrl($image) }}" alt="{{ $product->name }}">
                </div>
                <div class="zoom-indicator">Hover to zoom</div>
            </a>
            @php $itemIndex++; @endphp
        @endforeach
        
        {{-- Navigation --}}
        @if ($totalMedia > 1)
            <button class="custom-gallery-nav custom-gallery-prev" onclick="customGallery.prev()">‹</button>
            <button class="custom-gallery-nav custom-gallery-next" onclick="customGallery.next()">›</button>
        @endif
        
        {{-- Counter --}}
        <div class="custom-gallery-counter">
            <span class="current-image">1</span>/<span class="total-images">{{ $totalMedia }}</span>
        </div>
    </div>
    
    {{-- Thumbnails --}}
    @if ($totalMedia > 1)
        <div class="custom-gallery-thumbnails">
            @php $thumbIndex = 0; @endphp
            
            {{-- Video thumbnails --}}
            @if (!empty($product->video))
                @foreach($product->video as $video)
                    @continue(!$video['url'])
                    <div class="custom-gallery-thumb {{ $thumbIndex === 0 ? 'active' : '' }}" onclick="customGallery.goTo({{ $thumbIndex }})">
                        <img src="{{ $video['thumbnail'] ?? asset('images/video-placeholder.jpg') }}" alt="{{ $product->name }}">
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 20px;">▶</div>
                    </div>
                    @php $thumbIndex++; @endphp
                @endforeach
            @endif
            
            {{-- Image thumbnails --}}
            @foreach ($productImages as $image)
                <div class="custom-gallery-thumb {{ $thumbIndex === 0 ? 'active' : '' }}" onclick="customGallery.goTo({{ $thumbIndex }})">
                    {{ RvMedia::image($image, $product->name, 'thumb') }}
                </div>
                @php $thumbIndex++; @endphp
            @endforeach
        </div>
    @endif
</div>

<script>
window.customGallery = {
    currentIndex: 0,
    totalItems: {{ $totalMedia }},
    
    init: function() {
        this.updateDisplay();
        this.initLightGallery();
    },
    
    goTo: function(index) {
        if (index >= 0 && index < this.totalItems) {
            this.currentIndex = index;
            this.updateDisplay();
            // Reinitialize zoom for new active image
            setTimeout(() => this.initZoomEffect(), 100);
        }
    },
    
    next: function() {
        this.goTo((this.currentIndex + 1) % this.totalItems);
    },
    
    prev: function() {
        this.goTo((this.currentIndex - 1 + this.totalItems) % this.totalItems);
    },
    
    updateDisplay: function() {
        // Update main gallery
        document.querySelectorAll('.custom-gallery-item').forEach((item, index) => {
            item.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update thumbnails
        document.querySelectorAll('.custom-gallery-thumb').forEach((thumb, index) => {
            thumb.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update counter
        document.querySelector('.current-image').textContent = this.currentIndex + 1;
    },
    
    initLightGallery: function() {
        if (typeof $.fn.lightGallery !== 'undefined') {
            $('.custom-gallery-main').lightGallery({
                selector: 'a.custom-gallery-item',
                thumbnail: true,
                share: false,
                fullScreen: false,
                autoplay: false,
                autoplayControls: false,
                actualSize: false,
            });
        }
    },

    initZoomEffect: function() {
        // Only enable zoom on desktop
        if (window.innerWidth < 992) return;

        const galleryItems = document.querySelectorAll('.custom-gallery-item');

        galleryItems.forEach(item => {
            const img = item.querySelector('img');
            const lens = item.querySelector('.zoom-lens');
            const result = item.querySelector('.zoom-result');
            const resultImg = result.querySelector('img');

            if (!img || !lens || !result || !resultImg) return;

            let cx, cy;

            // Calculate zoom ratio
            function calculateZoom() {
                cx = result.offsetWidth / lens.offsetWidth;
                cy = result.offsetHeight / lens.offsetHeight;
                resultImg.style.width = (img.width * cx) + 'px';
                resultImg.style.height = (img.height * cy) + 'px';
            }

            // Mouse move handler
            function moveLens(e) {
                e.preventDefault();
                const pos = getCursorPos(e);
                let x = pos.x - (lens.offsetWidth / 2);
                let y = pos.y - (lens.offsetHeight / 2);

                // Prevent lens from going outside image
                if (x > img.width - lens.offsetWidth) x = img.width - lens.offsetWidth;
                if (x < 0) x = 0;
                if (y > img.height - lens.offsetHeight) y = img.height - lens.offsetHeight;
                if (y < 0) y = 0;

                // Set lens position
                lens.style.left = x + 'px';
                lens.style.top = y + 'px';

                // Display corresponding part in result
                resultImg.style.left = '-' + (x * cx) + 'px';
                resultImg.style.top = '-' + (y * cy) + 'px';
            }

            // Get cursor position relative to image
            function getCursorPos(e) {
                const rect = img.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                return { x: x, y: y };
            }

            // Mouse enter
            img.addEventListener('mouseenter', function() {
                if (item.classList.contains('active')) {
                    lens.style.display = 'block';
                    result.style.display = 'block';
                    calculateZoom();
                }
            });

            // Mouse leave
            img.addEventListener('mouseleave', function() {
                lens.style.display = 'none';
                result.style.display = 'none';
            });

            // Mouse move
            img.addEventListener('mousemove', moveLens);
            lens.addEventListener('mousemove', moveLens);
        });
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    customGallery.init();
    customGallery.initZoomEffect();
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft') {
        customGallery.prev();
    } else if (e.key === 'ArrowRight') {
        customGallery.next();
    }
});
</script>
