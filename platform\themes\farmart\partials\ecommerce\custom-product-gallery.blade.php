@php
    EcommerceHelper::registerThemeAssets();
    $version = get_cms_version();
    Theme::asset()->add('lightgallery-css', 'vendor/core/plugins/ecommerce/libraries/lightgallery/css/lightgallery.min.css', version: $version);
    Theme::asset()->container('footer')->add('lightgallery-js', 'vendor/core/plugins/ecommerce/libraries/lightgallery/js/lightgallery.min.js', ['jquery'], version: $version);

    $galleryStyle = theme_option('ecommerce_product_gallery_image_style', 'vertical');
    $totalImages = count($productImages);
    $totalVideos = 0;
    if (!empty($product->video)) {
        foreach($product->video as $video) {
            if ($video['url']) {
                $totalVideos++;
            }
        }
    }
    $totalMedia = $totalImages + $totalVideos;
@endphp

<style>
    /* Custom Image Gallery - No Slick, No Stacking */
    .custom-product-gallery {
        position: relative;
        width: 100%;
        min-height: 400px;
    }
    
    .custom-gallery-main {
        position: relative;
        width: 100%;
        height: 400px;
        overflow: hidden;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .custom-gallery-item {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        text-align: center;
    }
    
    .custom-gallery-item.active {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .custom-gallery-item img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    
    .custom-gallery-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0,0,0,0.6);
        color: white;
        border: none;
        width: 45px;
        height: 45px;
        border-radius: 50%;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        transition: all 0.3s ease;
    }
    
    .custom-gallery-nav:hover {
        background: rgba(0,0,0,0.8);
        transform: translateY(-50%) scale(1.1);
    }
    
    .custom-gallery-prev {
        left: 15px;
    }
    
    .custom-gallery-next {
        right: 15px;
    }
    
    .custom-gallery-counter {
        position: absolute;
        bottom: 15px;
        right: 15px;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 10;
    }
    
    .custom-gallery-thumbnails {
        display: flex;
        gap: 10px;
        margin-top: 15px;
        overflow-x: auto;
        padding: 10px 0;
    }
    
    .custom-gallery-thumb {
        flex-shrink: 0;
        width: 80px;
        height: 80px;
        border: 2px solid transparent;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .custom-gallery-thumb.active {
        border-color: #007bff;
    }
    
    .custom-gallery-thumb:hover {
        border-color: #0056b3;
        transform: scale(1.05);
    }
    
    .custom-gallery-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .custom-video-item {
        position: relative;
    }
    
    .custom-video-play {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.7);
        color: white;
        border: none;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        font-size: 24px;
        cursor: pointer;
        z-index: 5;
    }
</style>

<div class="custom-product-gallery">
    <div class="custom-gallery-main">
        @php $itemIndex = 0; @endphp
        
        {{-- Videos --}}
        @if (!empty($product->video))
            @foreach($product->video as $video)
                @continue(!$video['url'])
                <div class="custom-gallery-item custom-video-item {{ $itemIndex === 0 ? 'active' : '' }}" data-index="{{ $itemIndex }}">
                    @if ($video['provider'] === 'video')
                        <video controls style="max-width: 100%; max-height: 100%;">
                            <source src="{{ $video['url'] }}" type="video/mp4">
                        </video>
                    @else
                        <iframe 
                            src="{{ $video['url'] }}" 
                            style="width: 100%; height: 100%; border: none;"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    @endif
                </div>
                @php $itemIndex++; @endphp
            @endforeach
        @endif
        
        {{-- Images --}}
        @foreach ($productImages as $image)
            <a href="{{ RvMedia::getImageUrl($image) }}" class="custom-gallery-item {{ $itemIndex === 0 ? 'active' : '' }}" data-index="{{ $itemIndex }}">
                {{ RvMedia::image($image, $product->name) }}
            </a>
            @php $itemIndex++; @endphp
        @endforeach
        
        {{-- Navigation --}}
        @if ($totalMedia > 1)
            <button class="custom-gallery-nav custom-gallery-prev" onclick="customGallery.prev()">‹</button>
            <button class="custom-gallery-nav custom-gallery-next" onclick="customGallery.next()">›</button>
        @endif
        
        {{-- Counter --}}
        <div class="custom-gallery-counter">
            <span class="current-image">1</span>/<span class="total-images">{{ $totalMedia }}</span>
        </div>
    </div>
    
    {{-- Thumbnails --}}
    @if ($totalMedia > 1)
        <div class="custom-gallery-thumbnails">
            @php $thumbIndex = 0; @endphp
            
            {{-- Video thumbnails --}}
            @if (!empty($product->video))
                @foreach($product->video as $video)
                    @continue(!$video['url'])
                    <div class="custom-gallery-thumb {{ $thumbIndex === 0 ? 'active' : '' }}" onclick="customGallery.goTo({{ $thumbIndex }})">
                        <img src="{{ $video['thumbnail'] ?? asset('images/video-placeholder.jpg') }}" alt="{{ $product->name }}">
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 20px;">▶</div>
                    </div>
                    @php $thumbIndex++; @endphp
                @endforeach
            @endif
            
            {{-- Image thumbnails --}}
            @foreach ($productImages as $image)
                <div class="custom-gallery-thumb {{ $thumbIndex === 0 ? 'active' : '' }}" onclick="customGallery.goTo({{ $thumbIndex }})">
                    {{ RvMedia::image($image, $product->name, 'thumb') }}
                </div>
                @php $thumbIndex++; @endphp
            @endforeach
        </div>
    @endif
</div>

<script>
window.customGallery = {
    currentIndex: 0,
    totalItems: {{ $totalMedia }},
    
    init: function() {
        this.updateDisplay();
        this.initLightGallery();
    },
    
    goTo: function(index) {
        if (index >= 0 && index < this.totalItems) {
            this.currentIndex = index;
            this.updateDisplay();
        }
    },
    
    next: function() {
        this.goTo((this.currentIndex + 1) % this.totalItems);
    },
    
    prev: function() {
        this.goTo((this.currentIndex - 1 + this.totalItems) % this.totalItems);
    },
    
    updateDisplay: function() {
        // Update main gallery
        document.querySelectorAll('.custom-gallery-item').forEach((item, index) => {
            item.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update thumbnails
        document.querySelectorAll('.custom-gallery-thumb').forEach((thumb, index) => {
            thumb.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update counter
        document.querySelector('.current-image').textContent = this.currentIndex + 1;
    },
    
    initLightGallery: function() {
        if (typeof $.fn.lightGallery !== 'undefined') {
            $('.custom-gallery-main').lightGallery({
                selector: 'a.custom-gallery-item',
                thumbnail: true,
                share: false,
                fullScreen: false,
                autoplay: false,
                autoplayControls: false,
                actualSize: false,
            });
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    customGallery.init();
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft') {
        customGallery.prev();
    } else if (e.key === 'ArrowRight') {
        customGallery.next();
    }
});
</script>
