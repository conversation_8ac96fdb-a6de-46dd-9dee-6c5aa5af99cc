@php
    EcommerceHelper::registerThemeAssets();
    $version = get_cms_version();
    Theme::asset()->add('lightgallery-css', 'vendor/core/plugins/ecommerce/libraries/lightgallery/css/lightgallery.min.css', version: $version);
    Theme::asset()->container('footer')->add('lightgallery-js', 'vendor/core/plugins/ecommerce/libraries/lightgallery/js/lightgallery.min.js', ['jquery'], version: $version);

    $galleryStyle = theme_option('ecommerce_product_gallery_image_style', 'vertical');
    $totalImages = count($productImages);
    $totalVideos = 0;
    if (!empty($product->video)) {
        foreach($product->video as $video) {
            if ($video['url']) {
                $totalVideos++;
            }
        }
    }
    $totalMedia = $totalImages + $totalVideos;
@endphp

<style>
    /* Custom Image Gallery - No Slick, No Stacking */
    .custom-product-gallery {
        position: relative;
        width: 100%;
    }

    .custom-gallery-main {
        position: relative;
        width: 100%;
        height: auto;
        min-height: 300px;
        overflow: hidden;
        background: #fff;
        border-radius: 8px;
    }

    .custom-gallery-item {
        display: none;
        position: relative;
        width: 100%;
        text-align: center;
        line-height: 0;
    }

    .custom-gallery-item.active {
        display: block;
    }

    .custom-gallery-item img {
        width: 100%;
        height: auto;
        max-width: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
        cursor: zoom-in;
    }

    /* Desktop hover zoom effect */
    @media (min-width: 992px) {
        .custom-gallery-item {
            overflow: hidden;
            border-radius: 8px;
        }

        .custom-gallery-item img {
            height: 500px;
            object-fit: contain;
            background: #f8f9fa;
            cursor: zoom-in;
            transition: transform 0.4s ease-out;
        }

        .custom-gallery-item img:hover {
            transform: scale(1.3);
        }

        .custom-gallery-main {
            min-height: 500px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        /* Add zoom indicator */
        .custom-gallery-item::after {
            content: '🔍';
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(255,255,255,0.9);
            padding: 8px;
            border-radius: 50%;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 5;
        }

        .custom-gallery-item:hover::after {
            opacity: 1;
        }
    }

    /* Mobile adjustments */
    @media (max-width: 991px) {
        .custom-gallery-main {
            min-height: 300px;
        }

        .custom-gallery-item img {
            height: 300px;
            object-fit: contain;
            background: #f8f9fa;
        }
    }
    
    .custom-gallery-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255,255,255,0.9);
        color: #333;
        border: 1px solid rgba(0,0,0,0.1);
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        opacity: 0;
        visibility: hidden;
    }

    .custom-gallery-main:hover .custom-gallery-nav {
        opacity: 1;
        visibility: visible;
    }

    .custom-gallery-nav:hover {
        background: rgba(255,255,255,1);
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .custom-gallery-prev {
        left: 20px;
    }

    .custom-gallery-next {
        right: 20px;
    }

    /* Show navigation on mobile always */
    @media (max-width: 991px) {
        .custom-gallery-nav {
            opacity: 1;
            visibility: visible;
            background: rgba(0,0,0,0.6);
            color: white;
            width: 40px;
            height: 40px;
            font-size: 16px;
        }

        .custom-gallery-nav:hover {
            background: rgba(0,0,0,0.8);
        }

        .custom-gallery-prev {
            left: 10px;
        }

        .custom-gallery-next {
            right: 10px;
        }
    }
    
    .custom-gallery-counter {
        position: absolute;
        bottom: 20px;
        right: 20px;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        z-index: 10;
        font-weight: 500;
    }

    .custom-gallery-thumbnails {
        display: flex;
        gap: 8px;
        margin-top: 20px;
        overflow-x: auto;
        padding: 10px 0;
        scrollbar-width: thin;
    }

    .custom-gallery-thumbnails::-webkit-scrollbar {
        height: 4px;
    }

    .custom-gallery-thumbnails::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .custom-gallery-thumbnails::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    .custom-gallery-thumb {
        flex-shrink: 0;
        width: 70px;
        height: 70px;
        border: 2px solid transparent;
        border-radius: 6px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .custom-gallery-thumb.active {
        border-color: #007bff;
        box-shadow: 0 0 0 1px rgba(0,123,255,0.25);
    }

    .custom-gallery-thumb:hover {
        border-color: #0056b3;
        transform: scale(1.05);
    }

    .custom-gallery-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.2s ease;
    }

    .custom-gallery-thumb:hover img {
        transform: scale(1.1);
    }

    /* Desktop thumbnail adjustments */
    @media (min-width: 992px) {
        .custom-gallery-thumbnails {
            gap: 12px;
            margin-top: 25px;
        }

        .custom-gallery-thumb {
            width: 90px;
            height: 90px;
        }
    }
    
    .custom-video-item {
        position: relative;
    }
    
    .custom-video-play {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.7);
        color: white;
        border: none;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        font-size: 24px;
        cursor: pointer;
        z-index: 5;
    }
</style>

<div class="custom-product-gallery">
    <div class="custom-gallery-main">
        @php $itemIndex = 0; @endphp
        
        {{-- Videos --}}
        @if (!empty($product->video))
            @foreach($product->video as $video)
                @continue(!$video['url'])
                <div class="custom-gallery-item custom-video-item {{ $itemIndex === 0 ? 'active' : '' }}" data-index="{{ $itemIndex }}">
                    @if ($video['provider'] === 'video')
                        <video controls style="max-width: 100%; max-height: 100%;">
                            <source src="{{ $video['url'] }}" type="video/mp4">
                        </video>
                    @else
                        <iframe 
                            src="{{ $video['url'] }}" 
                            style="width: 100%; height: 100%; border: none;"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    @endif
                </div>
                @php $itemIndex++; @endphp
            @endforeach
        @endif
        
        {{-- Images --}}
        @foreach ($productImages as $image)
            <a href="{{ RvMedia::getImageUrl($image) }}" class="custom-gallery-item {{ $itemIndex === 0 ? 'active' : '' }}" data-index="{{ $itemIndex }}">
                {{ RvMedia::image($image, $product->name) }}
            </a>
            @php $itemIndex++; @endphp
        @endforeach
        
        {{-- Navigation --}}
        @if ($totalMedia > 1)
            <button class="custom-gallery-nav custom-gallery-prev" onclick="customGallery.prev()">‹</button>
            <button class="custom-gallery-nav custom-gallery-next" onclick="customGallery.next()">›</button>
        @endif
        
        {{-- Counter --}}
        <div class="custom-gallery-counter">
            <span class="current-image">1</span>/<span class="total-images">{{ $totalMedia }}</span>
        </div>
    </div>
    
    {{-- Thumbnails --}}
    @if ($totalMedia > 1)
        <div class="custom-gallery-thumbnails">
            @php $thumbIndex = 0; @endphp
            
            {{-- Video thumbnails --}}
            @if (!empty($product->video))
                @foreach($product->video as $video)
                    @continue(!$video['url'])
                    <div class="custom-gallery-thumb {{ $thumbIndex === 0 ? 'active' : '' }}" onclick="customGallery.goTo({{ $thumbIndex }})">
                        <img src="{{ $video['thumbnail'] ?? asset('images/video-placeholder.jpg') }}" alt="{{ $product->name }}">
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 20px;">▶</div>
                    </div>
                    @php $thumbIndex++; @endphp
                @endforeach
            @endif
            
            {{-- Image thumbnails --}}
            @foreach ($productImages as $image)
                <div class="custom-gallery-thumb {{ $thumbIndex === 0 ? 'active' : '' }}" onclick="customGallery.goTo({{ $thumbIndex }})">
                    {{ RvMedia::image($image, $product->name, 'thumb') }}
                </div>
                @php $thumbIndex++; @endphp
            @endforeach
        </div>
    @endif
</div>

<script>
window.customGallery = {
    currentIndex: 0,
    totalItems: {{ $totalMedia }},
    
    init: function() {
        this.updateDisplay();
        this.initLightGallery();
    },
    
    goTo: function(index) {
        if (index >= 0 && index < this.totalItems) {
            this.currentIndex = index;
            this.updateDisplay();
        }
    },
    
    next: function() {
        this.goTo((this.currentIndex + 1) % this.totalItems);
    },
    
    prev: function() {
        this.goTo((this.currentIndex - 1 + this.totalItems) % this.totalItems);
    },
    
    updateDisplay: function() {
        // Update main gallery
        document.querySelectorAll('.custom-gallery-item').forEach((item, index) => {
            item.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update thumbnails
        document.querySelectorAll('.custom-gallery-thumb').forEach((thumb, index) => {
            thumb.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update counter
        document.querySelector('.current-image').textContent = this.currentIndex + 1;
    },
    
    initLightGallery: function() {
        if (typeof $.fn.lightGallery !== 'undefined') {
            $('.custom-gallery-main').lightGallery({
                selector: 'a.custom-gallery-item',
                thumbnail: true,
                share: false,
                fullScreen: false,
                autoplay: false,
                autoplayControls: false,
                actualSize: false,
            });
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    customGallery.init();
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft') {
        customGallery.prev();
    } else if (e.key === 'ArrowRight') {
        customGallery.next();
    }
});
</script>
