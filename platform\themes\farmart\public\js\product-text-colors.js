document.addEventListener('DOMContentLoaded', function() {
    // Function to apply styles
    function applyStyles() {
        // Set product title color to black
        const productTitles = document.querySelectorAll('.product__title a, h3.product__title a, .product-inner .product__title a');
        productTitles.forEach(function(title) {
            title.style.color = '#000000';
            title.style.fontWeight = '500';

            // Add hover events
            title.addEventListener('mouseover', function() {
                this.style.color = '#ff6633';
            });

            title.addEventListener('mouseout', function() {
                this.style.color = '#000000';
            });
        });

        // Set vendor name color and styling
        const vendorNames = document.querySelectorAll('.sold-by-meta a, .product-inner .sold-by-meta a');
        vendorNames.forEach(function(vendor) {
            // Apply base styles
            vendor.style.color = '#000080';
            vendor.style.border = '1px solid #000080';
            vendor.style.borderRadius = '4px';
            vendor.style.padding = '2px 8px';
            vendor.style.display = 'inline-block';
            vendor.style.marginBottom = '5px';
            vendor.style.textDecoration = 'none';
            vendor.style.fontSize = '12px';
            vendor.style.fontWeight = '500';
            vendor.style.transition = 'all 0.3s ease';
            vendor.style.backgroundColor = 'rgba(0, 0, 128, 0.05)';

            // Add hover events
            vendor.addEventListener('mouseover', function() {
                this.style.color = '#ff6633';
                this.style.borderColor = '#ff6633';
                this.style.backgroundColor = 'rgba(255, 102, 51, 0.05)';
                this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            });

            vendor.addEventListener('mouseout', function() {
                this.style.color = '#000080';
                this.style.borderColor = '#000080';
                this.style.backgroundColor = 'rgba(0, 0, 128, 0.05)';
                this.style.boxShadow = 'none';
            });
        });

        // Apply styles to any product links that might be missed
        document.querySelectorAll('a[href*="products/"]').forEach(function(link) {
            if (link.closest('.product__title')) {
                link.style.color = '#000000';
            }
        });

        // Apply styles to any store links that might be missed
        document.querySelectorAll('a[href*="stores/"]').forEach(function(link) {
            if (link.closest('.sold-by-meta')) {
                link.style.color = '#000080';
                link.style.border = '1px solid #000080';
                link.style.borderRadius = '4px';
                link.style.padding = '2px 8px';
                link.style.display = 'inline-block';
                link.style.marginBottom = '5px';
                link.style.textDecoration = 'none';
                link.style.fontSize = '12px';
                link.style.fontWeight = '500';
                link.style.transition = 'all 0.3s ease';
                link.style.backgroundColor = 'rgba(0, 0, 128, 0.05)';

                // Add hover events if they don't already exist
                if (!link._hasHoverEvents) {
                    link.addEventListener('mouseover', function() {
                        this.style.color = '#ff6633';
                        this.style.borderColor = '#ff6633';
                        this.style.backgroundColor = 'rgba(255, 102, 51, 0.05)';
                        this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
                    });

                    link.addEventListener('mouseout', function() {
                        this.style.color = '#000080';
                        this.style.borderColor = '#000080';
                        this.style.backgroundColor = 'rgba(0, 0, 128, 0.05)';
                        this.style.boxShadow = 'none';
                    });

                    link._hasHoverEvents = true;
                }
            }
        });
    }

    // Apply styles immediately
    applyStyles();

    // Apply styles again after a short delay to catch any dynamically loaded elements
    setTimeout(applyStyles, 500);
    setTimeout(applyStyles, 1000);

    // Apply styles on any DOM changes
    const observer = new MutationObserver(function() {
        applyStyles();
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
