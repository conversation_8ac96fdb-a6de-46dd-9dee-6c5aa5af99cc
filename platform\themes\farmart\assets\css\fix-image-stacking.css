/* Fix for image stacking issue - hide images until slider is initialized */
.bb-product-gallery-images:not(.slick-initialized) > a:not(:first-child),
.bb-quick-view-gallery-images:not(.slick-initialized) > a:not(:first-child),
.product-gallery__wrapper:not(.slick-initialized) > a:not(:first-child),
.slick-slides-carousel:not(.slick-initialized) > *:not(:first-child),
.product-deals-day__body:not(.slick-initialized) > *:not(:first-child),
.featured-brands__body:not(.slick-initialized) > *:not(:first-child),
.product-categories-body:not(.slick-initialized) > *:not(:first-child) {
    display: none !important;
}

/* Ensure first image is visible while loading */
.bb-product-gallery-images:not(.slick-initialized) > a:first-child,
.bb-quick-view-gallery-images:not(.slick-initialized) > a:first-child,
.product-gallery__wrapper:not(.slick-initialized) > a:first-child,
.slick-slides-carousel:not(.slick-initialized) > *:first-child,
.product-deals-day__body:not(.slick-initialized) > *:first-child,
.featured-brands__body:not(.slick-initialized) > *:first-child,
.product-categories-body:not(.slick-initialized) > *:first-child {
    display: block !important;
}

/* Additional fixes for common carousel containers */
.slick-track:not(.slick-initialized) .slick-slide:not(:first-child) {
    display: none !important;
}

/* Fix for product galleries specifically */
.product-gallery:not(.slick-initialized) .product-gallery__image:not(:first-child) {
    display: none !important;
}

/* Fix for any carousel that hasn't been initialized yet */
[class*="carousel"]:not(.slick-initialized) > *:not(:first-child),
[class*="slider"]:not(.slick-initialized) > *:not(:first-child),
[class*="gallery"]:not(.slick-initialized) > *:not(:first-child) {
    display: none !important;
}

/* Ensure proper display once initialized */
.slick-initialized .slick-slide,
.slick-initialized > * {
    display: block !important;
}

/* Fix for homepage sliders and carousels */
.widget-slider:not(.slick-initialized) .slider-item:not(:first-child),
.widget-banner:not(.slick-initialized) .banner-item:not(:first-child),
.widget-product-categories:not(.slick-initialized) .category-item:not(:first-child),
.widget-featured-brands:not(.slick-initialized) .brand-item:not(:first-child) {
    display: none !important;
}

/* Fix for mobile carousels */
@media (max-width: 768px) {
    .mobile-carousel:not(.slick-initialized) > *:not(:first-child) {
        display: none !important;
    }
}
