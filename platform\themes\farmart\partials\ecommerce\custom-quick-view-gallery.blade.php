@php
    $totalImages = count($productImages);
@endphp

<style>
    /* Custom Quick View Gallery - No Slick, No Stacking */
    .custom-quick-view-gallery {
        position: relative;
        width: 100%;
        height: 400px;
    }
    
    .custom-quick-gallery-main {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .custom-quick-gallery-item {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        text-align: center;
    }
    
    .custom-quick-gallery-item.active {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .custom-quick-gallery-item img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    
    .custom-quick-gallery-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0,0,0,0.6);
        color: white;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        transition: all 0.3s ease;
    }
    
    .custom-quick-gallery-nav:hover {
        background: rgba(0,0,0,0.8);
        transform: translateY(-50%) scale(1.1);
    }
    
    .custom-quick-gallery-prev {
        left: 10px;
    }
    
    .custom-quick-gallery-next {
        right: 10px;
    }
    
    .custom-quick-gallery-counter {
        position: absolute;
        bottom: 10px;
        right: 10px;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        z-index: 10;
    }
    
    .custom-quick-gallery-dots {
        position: absolute;
        bottom: 15px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 8px;
        z-index: 10;
    }
    
    .custom-quick-gallery-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255,255,255,0.5);
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .custom-quick-gallery-dot.active {
        background: white;
        transform: scale(1.2);
    }
</style>

<div class="custom-quick-view-gallery">
    <div class="custom-quick-gallery-main">
        @foreach ($productImages as $index => $image)
            <a href="{{ RvMedia::getImageUrl($image) }}" class="custom-quick-gallery-item {{ $index === 0 ? 'active' : '' }}" data-index="{{ $index }}">
                {{ RvMedia::image($image, $product->name, 'medium') }}
            </a>
        @endforeach
        
        {{-- Navigation --}}
        @if ($totalImages > 1)
            <button class="custom-quick-gallery-nav custom-quick-gallery-prev" onclick="customQuickGallery.prev()">‹</button>
            <button class="custom-quick-gallery-nav custom-quick-gallery-next" onclick="customQuickGallery.next()">›</button>
        @endif
        
        {{-- Counter --}}
        <div class="custom-quick-gallery-counter">
            <span class="current-image">1</span>/<span class="total-images">{{ $totalImages }}</span>
        </div>
        
        {{-- Dots --}}
        @if ($totalImages > 1 && $totalImages <= 5)
            <div class="custom-quick-gallery-dots">
                @for ($i = 0; $i < $totalImages; $i++)
                    <div class="custom-quick-gallery-dot {{ $i === 0 ? 'active' : '' }}" onclick="customQuickGallery.goTo({{ $i }})"></div>
                @endfor
            </div>
        @endif
    </div>
</div>

<script>
window.customQuickGallery = {
    currentIndex: 0,
    totalItems: {{ $totalImages }},
    
    init: function() {
        this.updateDisplay();
        this.initLightGallery();
        this.initSwipeGestures();
    },
    
    goTo: function(index) {
        if (index >= 0 && index < this.totalItems) {
            this.currentIndex = index;
            this.updateDisplay();
        }
    },
    
    next: function() {
        this.goTo((this.currentIndex + 1) % this.totalItems);
    },
    
    prev: function() {
        this.goTo((this.currentIndex - 1 + this.totalItems) % this.totalItems);
    },
    
    updateDisplay: function() {
        // Update main gallery
        document.querySelectorAll('.custom-quick-gallery-item').forEach((item, index) => {
            item.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update dots
        document.querySelectorAll('.custom-quick-gallery-dot').forEach((dot, index) => {
            dot.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update counter
        const currentSpan = document.querySelector('.custom-quick-view-gallery .current-image');
        if (currentSpan) {
            currentSpan.textContent = this.currentIndex + 1;
        }
    },
    
    initLightGallery: function() {
        if (typeof $.fn.lightGallery !== 'undefined') {
            $('.custom-quick-gallery-main').lightGallery({
                selector: 'a.custom-quick-gallery-item',
                thumbnail: true,
                share: false,
                fullScreen: false,
                autoplay: false,
                autoplayControls: false,
                actualSize: false,
            });
        }
    },
    
    initSwipeGestures: function() {
        let startX = 0;
        let startY = 0;
        const gallery = document.querySelector('.custom-quick-gallery-main');
        
        if (!gallery) return;
        
        gallery.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        gallery.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // Only trigger if horizontal swipe is more significant than vertical
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    this.next(); // Swipe left - next image
                } else {
                    this.prev(); // Swipe right - previous image
                }
            }
            
            startX = 0;
            startY = 0;
        });
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Delay initialization slightly to ensure modal is fully loaded
    setTimeout(function() {
        if (document.querySelector('.custom-quick-view-gallery')) {
            customQuickGallery.init();
        }
    }, 100);
});

// Re-initialize when modal is shown
$(document).on('shown.bs.modal', '.bb-quick-view-modal', function() {
    setTimeout(function() {
        if (document.querySelector('.custom-quick-view-gallery')) {
            customQuickGallery.init();
        }
    }, 100);
});

// Keyboard navigation for quick view
document.addEventListener('keydown', function(e) {
    if (document.querySelector('.bb-quick-view-modal.show')) {
        if (e.key === 'ArrowLeft') {
            customQuickGallery.prev();
        } else if (e.key === 'ArrowRight') {
            customQuickGallery.next();
        }
    }
});
</script>
