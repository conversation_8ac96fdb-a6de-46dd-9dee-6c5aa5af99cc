// <PERSON>ript to completely remove all action buttons from product cards
(function() {
    // Function to remove all action buttons
    function removeActionButtons() {
        // Find all button containers and remove them completely from the DOM
        const buttonSelectors = [
            // All product loop buttons
            ".product-loop__buttons",
            ".quick-view-button",
            ".wishlist-button",
            ".compare-button",
            ".product-loop_button",
            ".product-loop_action",
            // Product inner specific
            ".product-inner .product-loop__buttons",
            ".product-inner .quick-view-button",
            ".product-inner .wishlist-button",
            ".product-inner .compare-button",
            ".product-inner .product-loop_button",
            ".product-inner .product-loop_action",
            // Featured products specific
            ".featured-products .product-loop__buttons",
            ".featured-products .quick-view-button",
            ".featured-products .wishlist-button",
            ".featured-products .compare-button",
            // Essential products specific
            ".essential-products .product-loop__buttons",
            ".essential-products .quick-view-button",
            ".essential-products .wishlist-button",
            ".essential-products .compare-button",
            // Widget products specific
            ".widget-products-with-category .product-loop__buttons",
            ".widget-products-with-category .quick-view-button",
            ".widget-products-with-category .wishlist-button",
            ".widget-products-with-category .compare-button",
            // Slick slide specific
            ".slick-slide .product-loop__buttons",
            ".slick-slide .quick-view-button",
            ".slick-slide .wishlist-button",
            ".slick-slide .compare-button"
        ];

        buttonSelectors.forEach(function(selector) {
            const elements = document.querySelectorAll(selector);
            elements.forEach(function(element) {
                if (element) {
                    // Hide it first to prevent flashing
                    element.style.display = "none";
                    element.style.visibility = "hidden";
                    element.style.opacity = "0";
                    element.style.pointerEvents = "none";
                    element.style.width = "0";
                    element.style.height = "0";
                    element.style.position = "absolute";
                    element.style.zIndex = "-9999";
                    element.style.clip = "rect(0, 0, 0, 0)";
                    
                    // Then try to remove it from the DOM
                    try {
                        element.remove();
                    } catch (e) {
                        // If remove fails, at least it's hidden
                        console.log("Could not remove element, but it's hidden:", e);
                    }
                }
            });
        });
    }

    // Run immediately
    removeActionButtons();

    // Run when DOM is loaded
    document.addEventListener("DOMContentLoaded", removeActionButtons);

    // Run periodically to catch any dynamically added elements
    setInterval(removeActionButtons, 100);
})();
