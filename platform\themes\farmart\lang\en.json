{"(:count reviews)": "(:count reviews)", "(:count)": "(:count)", "(Shipping fees not included)": "(Shipping fees not included)", "-- None --": "-- None --", "-- Select --": "-- Select --", "-- select --": "-- select --", "1 Review": "1 Review", "404 - Not found": "404 - Not found", "404 Page Not Found": "404 Page Not Found", "404 page image": "404 page image", "500 Internal Server Error": "500 Internal Server Error", "503 Service Unavailable": "503 Service Unavailable", ":count Reviews": ":count Reviews", ":count decrease": ":count decrease", ":count increase": ":count increase", ":count more": ":count more", ":customer has requested return product(s)": ":customer has requested return product(s)", ":name doesn't support :currency. List of currencies supported by :name: :currencies.": ":name doesn't support :currency. List of currencies supported by :name: :currencies.", ":name font family": ":name font family", ":name font size": ":name font size", ":number Star": ":number Star", ":number Stars": ":number Stars", ":number product available": ":number product available", ":number products available": ":number products available", ":price for :total item(s)": ":price for :total item(s)", ":product is already in your compare list!": ":product is already in your compare list!", ":total Product found": ":total Product found", ":total Product(s) found": ":total Product(s) found", ":total Products found": ":total Products found", ":total review(s) \":star star\" for \":product\"": ":total review(s) \":star star\" for \":product\"", ":total review(s) for \":product\"": ":total review(s) for \":product\"", "A new version (:version / released on :date) is available to update!": "A new version (:version / released on :date) is available to update!", "API Key": "API Key", "Accept and install": "Accept and install", "Accepted Payment methods": "Accepted Payment methods", "Accepted Payment methods link (optional)": "Accepted Payment methods link (optional)", "Account": "Account", "Account Holder Name": "Account Holder Name", "Account Number": "Account Number", "Account Settings": "Account <PERSON><PERSON>", "Account information": "Account information", "Action": "Action", "Actions": "Actions", "Ad :number": "Ad :number", "Add Google Maps iframe": "Add Google Maps iframe", "Add To Cart": "Add To Cart", "Add Wishlist": "Add Wishlist", "Add YouTube video": "Add YouTube video", "Add a custom menu to your widget area.": "Add a custom menu to your widget area.", "Add a new address": "Add a new address", "Add custom HTML content": "Add custom HTML content", "Add new": "Add new", "Add new address...": "Add new address...", "Add to Cart": "Add to Cart", "Add to cart": "Add to cart", "Add your review": "Add your review", "Added product :product successfully!": "Added product :product successfully!", "Added product :product to cart successfully!": "Added product :product to cart successfully!", "Added product :product to compare list successfully!": "Added product :product to compare list successfully!", "Added review successfully!": "Added review successfully!", "Address": "Address", "Address :number": "Address :number", "Address appears to be incomplete": "Address appears to be incomplete", "Address books": "Address books", "Addresses": "Addresses", "Admin": "Admin", "Ads": "Ads", "After cancel amount and fee will be refunded back in your balance": "After cancel amount and fee will be refunded back in your balance", "After registration at :name, you will have API key": "After registration at :name, you will have API key", "After registration at :name, you will have Client ID, Client Secret": "After registration at :name, you will have Client ID, Client Secret", "After registration at :name, you will have Public & Secret keys": "After registration at :name, you will have Public & Secret keys", "After registration at :name, you will have Store ID and Store Password (API/Secret key)": "After registration at :name, you will have Store ID and Store Password (API/Secret key)", "All": "All", "All Articles": "All Articles", "All Categories": "All Categories", "All Offers": "All Offers", "All categories": "All categories", "All files": "All files", "All messages are recorded and spam is not tolerated. Your email address will be shown to the recipient.": "All messages are recorded and spam is not tolerated. Your email address will be shown to the recipient.", "Already have an account?": "Already have an account?", "Amount": "Amount", "An error occurred while trying to login": "An error occurred while trying to login", "App Android Image": "App Android Image", "App Android Link": "App Android Link", "App Background": "App Background", "App Description": "App Description", "App Title": "App Title", "App iOS Image": "App iOS Image", "App iOS Link": "App iOS Link", "Applied coupon \":code\" successfully!": "Applied coupon \":code\" successfully!", "Apply": "Apply", "Approving": "Approving", "Are you sure you want to delete this address?": "Are you sure you want to delete this address?", "Are you sure you want to do this?": "Are you sure you want to do this?", "Are you sure you want to turn off the debug mode? This action cannot be undone.": "Are you sure you want to turn off the debug mode? This action cannot be undone.", "Are you sure?": "Are you sure?", "Attract your customers with the best products.": "Attract your customers with the best products.", "Autoplay speed (if autoplay enabled)": "Autoplay speed (if autoplay enabled)", "Availability": "Availability", "Available": "Available", "Back": "Back", "Back to Home": "Back to Home", "Back to cart": "Back to cart", "Back to login page": "Back to login page", "Back to shopping": "Back to shopping", "Background": "Background", "Background Image": "Background Image", "Background color": "Background color", "Balance": "Balance", "Bank Code/IFSC": "Bank Code/IFSC", "Bank Name": "Bank Name", "Bank transfer amount: <strong>:amount</strong>": "Bank transfer amount: <strong>:amount</strong>", "Bank transfer description: <strong>Payment for order :code</strong>": "Bank transfer description: <strong>Payment for order :code</strong>", "Barcode \":value\" has been duplicated on row #:row": "Barcode \":value\" has been duplicated on row #:row", "Become A Vendor": "Become A Vendor", "Become Vendor": "<PERSON>come Vendor", "Become a Vendor?": "Become a Vendor?", "Below :toPrice": "Below :toP<PERSON>", "Billing information": "Billing information", "Blog Categories": "Blog Categories", "Blog Posts": "Blog Posts", "Blog Search": "Blog Search", "Bottom footer sidebar": "Bottom footer sidebar", "Bottom header background color": "Bottom header background color", "Bottom header text color": "Bottom header text color", "Brand": "Brand", "Brands": "Brands", "Browse products": "Browse products", "Business Name": "Business Name", "Buy Now": "Buy Now", "By": "By", "By :name": "By :name", "By Price": "By Price", "Can't send message on this time, please try again later!": "Can't send message on this time, please try again later!", "Cancel": "Cancel", "Cancel Order": "Cancel Order", "Cancel order": "Cancel order", "Cancel return order with reason: :reason": "Cancel return order with reason: :reason", "Cancellation Reason": "Cancellation Reason", "Cannot download files": "Cannot download files", "Cannot find this customer!": "Cannot find this customer!", "Cannot found files": "Cannot found files", "Cannot login, no email provided!": "Cannot login, no email provided!", "Captcha": "<PERSON><PERSON>", "Captcha Verification Failed!": "Captcha Verification Failed!", "Cart": "<PERSON><PERSON>", "Cart item ID is required!": "Cart item ID is required!", "Cart item is not existed!": "Cart item is not existed!", "Cart totals": "Cart totals", "Categories": "Categories", "Category": "Category", "Change Password": "Change Password", "Change avatar": "Change avatar", "Change copyright": "Change copyright", "Change password": "Change password", "Check": "Check", "Checkout": "Checkout", "Checkout error!": "Checkout error!", "Checkout successfully!": "Checkout successfully!", "Choose Reason": "Choose <PERSON>", "Choose a Reason for Order Cancellation": "Choose a Reason for Order Cancellation", "Choose a reason...": "Choose a reason...", "Choose categories": "Choose categories", "Choose date format for your front theme.": "Choose date format for your front theme.", "Choose products": "Choose products", "City": "City", "Close": "Close", "Color": "Color", "Coming Soon": "Coming Soon", "Comment": "Comment", "Company address": "Company address", "Company email": "Company email", "Company name": "Company name", "Company tax code": "Company tax code", "Compare": "Compare", "Completed At": "Completed At", "Completed at": "Completed at", "Config email templates for theme": "Config email templates for theme", "Confirm password": "Confirm password", "Confirm your password": "Confirm your password", "Congratulations on being a vendor at :site_title": "Congratulations on being a vendor at :site_title", "Connect social networks title": "Connect social networks title", "Contact Seller": "<PERSON>ller", "Contact Vendor": "Contact <PERSON>", "Contact info boxes": "Contact info boxes", "Contact seller message": "Contact seller message", "Content": "Content", "Content delivery network (CDN)": "Content delivery network (CDN)", "Continue Shopping": "Continue Shopping", "Continue shopping": "Continue shopping", "Continue with <strong>:provider</strong>": "Continue with <strong>:provider</strong>", "Copy link": "Copy link", "Copyright": "Copyright", "Copyright on footer of site": "Copyright on footer of site", "Copyright on footer of site. Using %Y to display current year.": "Copyright on footer of site. Using %Y to display current year.", "Copyright text at the bottom footer.": "Copyright text at the bottom footer.", "Could not download updated file. Please check your license or your internet network.": "Could not download updated file. Please check your license or your internet network.", "Could not update files & database.": "Could not update files & database.", "Country": "Country", "Coupon": "Coupon", "Coupon code": "Coupon code", "Coupon code discount amount": "Coupon code discount amount", "Coupon code is not valid or does not apply to the products": "Coupon code is not valid or does not apply to the products", "Coupon code: \":code\"": "Coupon code: \":code\"", "Coupon code: :code": "Coupon code: :code", "Coupon codes (:count)": "Coupon codes (:count)", "Coupons": "Coupons", "Cover Image": "Cover Image", "Create": "Create", "Create Address": "Create Address", "Create a new product <a href=\":url\">here</a>": "Create a new product <a href=\":url\">here</a>", "Create coupon": "Create coupon", "Created At": "Created At", "Created at": "Created at", "Created shipment for order": "Created shipment for order", "Current password": "Current password", "Custom HTML": "Custom HTML", "Custom Menu": "Custom Menu", "Customer": "Customer", "Customer Email": "Customer <PERSON><PERSON>", "Customer Name": "Customer Name", "Customer Recently Viewed Products": "Customer Recently Viewed Products", "Customer can buy product and pay directly using Visa, Credit card via :name": "Customer can buy product and pay directly using Visa, Credit card via :name", "Customer dashboard": "Customer dashboard", "Customer forgot password form": "Customer forgot password form", "Customer information": "Customer information", "Customer login form": "Customer login form", "Customer register form": "Customer register form", "Customer reset password form": "Customer reset password form", "Customer reviews": "Customer reviews", "Customers who bought this item also bought": "Customers who bought this item also bought", "Dashboard": "Dashboard", "Date": "Date", "Date Shipped": "Date Shipped", "Date format": "Date format", "Date of birth": "Date of birth", "Default": "<PERSON><PERSON><PERSON>", "Default: 3": "Default: 3", "Delete": "Delete", "Delete account": "Delete account", "Delete ads.txt file": "Delete ads.txt file", "Delete your account": "Delete your account", "Deleted review successfully!": "Deleted review successfully!", "Delivered": "Delivered", "Delivery Notes": "Delivery Notes", "Delivery Notes:": "Delivery Notes:", "Description": "Description", "Discount": "Discount", "Discount :amount": "Discount :amount", "Discount :percentage%": "Discount :percentage%", "Discount amount": "Discount amount", "Discount promotion": "Discount promotion", "Display Ads on sidebar": "Display Ads on sidebar", "Display Become a vendor on product detail sidebar": "Display Become a vendor on product detail sidebar", "Display Newsletter form on sidebar": "Display Newsletter form on sidebar", "Display Site features on sidebar": "Display Site features on sidebar", "Display blog posts": "Display blog posts", "Display posts count?": "Display posts count?", "Display recent blog posts": "Display recent blog posts", "Do you really want to delete the review?": "Do you really want to delete the review?", "Do you want to cancel?": "Do you want to cancel?", "Do you want to delete this image?": "Do you want to delete this image?", "Documents": "Documents", "Don't have an account?": "Don't have an account?", "Don't show this popup again": "Don't show this popup again", "Download all files": "Download all files", "Download invoice": "Download invoice", "Download product \":name\" with external links": "Download product \":name\" with external links", "Downloads": "Downloads", "Drop Us A Line": "Drop Us A Line", "Drop files here or click to upload.": "Drop files here or click to upload.", "Earnings": "Earnings", "Earnings in :label": "Earnings in :label", "Edit": "Edit", "Edit Address #:id": "Edit Address #:id", "Edit this shortcode": "Edit this shortcode", "Edit this widget": "Edit this widget", "Email": "Email", "Email (optional)": "Email (optional)", "Email :number": "Email :number", "Email :store": "Email :store", "Email Address": "Email Address", "Email address": "Email address", "Email or Phone number": "Email or Phone number", "Email or phone": "Email or phone", "Email will be sent to the seller when someone contact from store profile page": "Email will be sent to the seller when someone contact from store profile page", "Empty cart successfully!": "Empty cart successfully!", "Enable Facebook chat?": "Enable Facebook chat?", "Enable Facebook comment in post detail page?": "Enable Facebook comment in post detail page?", "Enable Facebook comment in the product page?": "Enable Facebook comment in the product page?", "Enable Newsletter Popup": "Enable Newsletter Popup", "Enable Preloader?": "Enable Preloader?", "Enable dark mode": "Enable dark mode", "Enable lazy load images?": "Enable lazy load images?", "Enable lazy loading": "Enable lazy loading", "Enable light mode": "Enable light mode", "Enable shop by categories on header?": "Enable shop by categories on header?", "Enable social sharing?": "Enable social sharing?", "Enable sticky header on mobile?": "Enable sticky header on mobile?", "Enable sticky header?": "Enable sticky header?", "End date": "End date", "Enter API key into the box in right hand": "Enter API key into the box in right hand", "Enter Client ID, Secret into the box in right hand": "Enter Client ID, Secret into the box in right hand", "Enter Coupon Code": "Enter Coupon Code", "Enter Public, Secret into the box in right hand": "Enter Public, Secret into the box in right hand", "Enter Store ID and Store Password (API/Secret key) into the box in right hand": "Enter Store ID and Store Password (API/Secret key) into the box in right hand", "Enter Your Email": "Enter Your Email", "Enter coupon code": "Enter coupon code", "Enter coupon code...": "Enter coupon code...", "Enter link for :name": "Enter link for :name", "Enter the order ID": "Enter the order ID", "Enter your email": "Enter your email", "Enter your phone number": "Enter your phone number", "Error": "Error", "Error when processing payment via :paymentType!": "Error when processing payment via :paymentType!", "Estimate Date Shipped": "Estimate Date Shipped", "Ex: 0943243332": "Ex: 0943243332", "Ex: My Shop": "Ex: My Shop", "Exception": "Exception", "Expires in": "Expires in", "Explore and add items to get started": "Explore and add items to get started", "Extended info": "Extended info", "External link downloads": "External link downloads", "FAQs": "FAQs", "Facebook": "Facebook", "Facebook Admin ID": "Facebook Admin ID", "Facebook Admins": "Facebook Admins", "Facebook App ID": "Facebook App ID", "Facebook Integration": "Facebook Integration", "Facebook admins to manage comments :link": "Facebook admins to manage comments :link", "Facebook page ID": "Facebook page ID", "Featured": "Featured", "Featured Blog Posts": "Featured Blog Posts", "Featured Brands": "Featured Brands", "Featured Product Categories": "Featured Product Categories", "Featured products": "Featured products", "Fee": "Fee", "Fees": "Fees", "Filter": "Filter", "Filter Products": "Filter Products", "Fix it for me": "Fix it for me", "Flash sale": "Flash sale", "Font Icon": "Font Icon", "Footer border color": "Footer border color", "Footer heading color": "Footer heading color", "Footer hover color": "Footer hover color", "Footer sidebar": "Footer sidebar", "Footer text color": "Footer text color", "For devices with width from 768px to 1200px, if empty, will use the image from the desktop.": "For devices with width from 768px to 1200px, if empty, will use the image from the desktop.", "For devices with width less than 768px, if empty, will use the image from the tablet.": "For devices with width less than 768px, if empty, will use the image from the tablet.", "Forgot Password": "Forgot Password", "Forgot password?": "Forgot password?", "Free shipping": "Free shipping", "Free shipping for all orders": "Free shipping for all orders", "Free shipping to <strong>:target</strong>": "Free shipping to <strong>:target</strong>", "On Purchase over ৳5000": "On Purchase over ৳5000", "7 Days Easy Return": "7 Days Easy Return", "From :fromPrice to :toPrice": "From :fromPrice to :toPrice", "From your account dashboard you can view your <a class=\"text-primary\" href=\":order\">recent orders</a>, manage your <a class=\"text-primary\" href=\":addresses\">shipping and billing addresses</a>, and <a class=\"text-primary\" href=\":edit_account\">edit your password and account details</a>.": "From your account dashboard you can view your <a class=\"text-primary\" href=\":order\">recent orders</a>, manage your <a class=\"text-primary\" href=\":addresses\">shipping and billing addresses</a>, and <a class=\"text-primary\" href=\":edit_account\">edit your password and account details</a>.", "Full Name": "Full Name", "Full Width": "Full Width", "Full name": "Full name", "Full width": "Full width", "Functions": "Functions", "Funding Source": "Funding Source", "Gift": "Gift", "Go To Cart": "Go To Cart", "Go back home": "Go back home", "Go to :link to change the copyright text.": "Go to :link to change the copyright text.", "Go to homepage": "Go to homepage", "Google Maps": "Google Maps", "Grid": "Grid", "HTML code": "HTML code", "Header Navigation": "Header Navigation", "Header bottom": "Header bottom", "Header deliver color": "Header deliver color", "Header middle": "Header middle", "Header top": "Header top", "Heading color": "Heading color", "Height": "Height", "Hello": "Hello", "Hello <strong>:name</strong>,": "Hello <strong>:name</strong>,", "Home": "Home", "Homepage": "Homepage", "Hotline": "Hotline", "Hotline 24/7:": "Hotline 24/7:", "Hotline Order": "Hotline Order", "Humanitarian Donation": "Humanitarian Donation", "Hurry up! Sale end in": "Hurry up! Sale end in", "I agree to the :link": "I agree to the :link", "I agree to the Terms and Privacy Policy": "I agree to the Terms and Privacy Policy", "I am a customer": "I am a customer", "I am a vendor": "I am a vendor", "ID": "ID", "ID number": "ID number", "Icon": "Icon", "Icon :number": "Icon :number", "Icon Image (It will override icon above if set)": "Icon Image (It will override icon above if set)", "Icon image": "Icon image", "Icon image (It will override icon above if set)": "Icon image (It will override icon above if set)", "If you are the administrator and you can't access your site after enabling maintenance mode, just need to delete file <strong>storage/framework/down</strong> to turn-off maintenance mode.": "If you are the administrator and you can't access your site after enabling maintenance mode, just need to delete file <strong>storage/framework/down</strong> to turn-off maintenance mode.", "If you need help, contact us at :mail.": "If you need help, contact us at :mail.", "Image": "Image", "Image placeholder": "Image placeholder", "Images from customer (:count)": "Images from customer (:count)", "In Stock": "In Stock", "In Transit": "In Transit", "In stock": "In stock", "Includes Completed, Pending, and Processing statuses": "Includes Completed, Pending, and Processing statuses", "Install plugin from Marketplace": "Install plugin from Marketplace", "Insufficient balance or no bank information": "Insufficient balance or no bank information", "Internal Server Error": "Internal Server Error", "Invalid Data!": "Invalid Data!", "Invalid Transaction!": "Invalid Transaction!", "Invalid signature of vendor info": "Invalid signature of vendor info", "Invalid step.": "Invalid step.", "InvalidStateException occurred while trying to login": "InvalidStateException occurred while trying to login", "Invoice detail :code": "Invoice detail :code", "Invoices": "Invoices", "Is autoplay?": "Is autoplay?", "It is optional. If you have UPI ID, you can provide it here. Learn more: https://support.google.com/pay/india/answer/********?hl=en": "It is optional. If you have UPI ID, you can provide it here. Learn more: https://support.google.com/pay/india/answer/********?hl=en", "It looks as through there are no activities here.": "It looks as through there are no activities here.", "It will replace Icon Font if it is present.": "It will replace Icon Font if it is present.", "Items Count": "Items Count", "Items Earning Sales: :amount": "Items Earning Sales: :amount", "Joined on :date": "Joined on :date", "Key": "Key", "Large": "Large", "Last update": "Last update", "Latest": "Latest", "Layout": "Layout", "Lazy load images": "Lazy load images", "Lazy load placeholder image": "Lazy load placeholder image", "Learn more": "Learn more", "Learn more about Twig template: :url": "Learn more about Twig template: :url", "Leave categories empty if you want to show posts from all categories.": "Leave categories empty if you want to show posts from all categories.", "Left :left": "Left :left", "License": "License", "License Activation": "License Activation", "Limit": "Limit", "Limit number of categories": "Limit number of categories", "Limit number of products": "Limit number of products", "LinkedIn": "LinkedIn", "List": "List", "List of product categories": "List of product categories", "Local disk": "Local disk", "Log files": "Log files", "Login": "<PERSON><PERSON>", "Login to your account": "Login to your account", "Login with social networks": "Login with social networks", "Logo height (px)": "Logo height (px)", "Logout": "Logout", "Looks like there are no reviews yet.": "Looks like there are no reviews yet.", "Loop?": "Loop?", "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.": "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.", "Manage Invoices": "Manage Invoices", "Marketplace Stores": "Marketplace Stores", "Math Captcha": "<PERSON>", "Math Captcha Verification Failed!": "Math Captcha Verification Failed!", "Maximum order quantity is :qty, please check your cart and retry again!": "Maximum order quantity is :qty, please check your cart and retry again!", "Maximum order quantity of product :product is :quantity!": "Maximum order quantity of product :product is :quantity! ", "Maximum quantity is :max!": "Maximum quantity is :max!", "Media URL": "Media URL", "Medium": "Medium", "Menu": "<PERSON><PERSON>", "Merchandise": "Merchandise", "Message sent via your market profile on {{ site_title }}": "Message sent via your market profile on {{ site_title }}", "Messages": "Messages", "Middle header background color": "Middle header background color", "Middle header text color": "Middle header text color", "Minimum order amount is :amount, you need to buy more :more to place an order!": "Minimum order amount is :amount, you need to buy more :more to place an order!", "Minimum order amount to use COD (Cash On Delivery) payment method is :amount, you need to buy more :more to place an order!": "Minimum order amount to use COD (Cash On Delivery) payment method is :amount, you need to buy more :more to place an order!", "Minimum order quantity is :qty, you need to buy more :more to place an order!": "Minimum order quantity is :qty, you need to buy more :more to place an order!", "Minimum order quantity of product :product is :quantity, you need to buy more :more to place an order!": "Minimum order quantity of product :product is :quantity, you need to buy more :more to place an order! ", "Minus": "Minus", "Mobile Image": "Mobile Image", "Moderator's note": "Moderator's note", "My Profile": "My Profile", "Name": "Name", "Name :number": "Name :number", "Name: A-Z": "Name: A-<PERSON>", "Name: Z-A": "Name: Z-<PERSON>", "New password": "New password", "Newest": "Newest", "Newsletter Popup": "Newsletter Popup", "Newsletter form": "Newsletter form", "No": "No", "No digital products!": "No digital products!", "No order return requests!": "No order return requests!", "No orders has been made yet.": "No orders has been made yet.", "No orders yet!": "No orders yet!", "No orders!": "No orders!", "No payment charge. Please try again!": "No payment charge. Please try again!", "No products in cart": "No products in cart", "No products in cart. :link!": "No products in cart. :link!", "No products in compare list!": "No products in compare list!", "No products in the cart.": "No products in the cart.", "No products in wishlist!": "No products in wishlist!", "No products!": "No products!", "No results found": "No results found", "No results found!": "No results found!", "No reviews!": "No reviews!", "No shipping methods available!": "No shipping methods available!", "No shipping methods were found with your provided shipping information!": "No shipping methods were found with your provided shipping information!", "No vendor found.": "No vendor found.", "Not Available": "Not Available", "Not available": "Not available", "Not available in COD payment option.": "Not available in COD payment option.", "Note": "Note", "Notes about your order, e.g. special notes for delivery.": "Notes about your order, e.g. special notes for delivery.", "Number tags to display": "Number tags to display", "Oldest": "Oldest", "One or all products are not enough quantity so cannot update!": "One or all products are not enough quantity so cannot update!", "Oops! Page not found.": "Oops! Page not found.", "Oops! Something Went Wrong.": "Oops! Something Went Wrong.", "Open user menu": "Open user menu", "Or login with": "Or login with", "Or you can upload a new one, the old one will be replaced.": "Or you can upload a new one, the old one will be replaced.", "Order ID": "Order ID", "Order ID number": "Order ID number", "Order Return Request not found!": "Order Return Request not found!", "Order Return Requests": "Order Return Requests", "Order Return Requests :id": "Order Return Requests :id", "Order Returns": "Order Returns", "Order completed at": "Order completed at", "Order detail :id": "Order detail :id", "Order information": "Order information", "Order information :order_id": "Order information :order_id", "Order is created from checkout page": "Order is created from checkout page", "Order is created from the checkout page": "Order is created from the checkout page", "Order notes": "Order notes", "Order number": "Order number", "Order status": "Order status", "Order successfully at :site_title": "Order successfully at :site_title", "Order successfully. Order number :id": "Order successfully. Order number :id", "Order tracking": "Order tracking", "Order tracking :code": "Order tracking :code", "Order was cancelled by customer :customer": "Order was cancelled by customer :customer", "Order was cancelled by customer :customer with reason :reason": "Order was cancelled by customer :customer with reason :reason", "Order was created from checkout page": "Order was created from checkout page", "Ordered at": "Ordered at", "Orders": "Orders", "Other": "Other", "Our Stores": "Our Stores", "Out Of Stock": "Out Of Stock", "Out of stock": "Out of stock", "Over :fromPrice": "Over :fromPrice", "Overview": "Overview", "PHP version :version required": "PHP version :version required", "Page could not be found": "Page could not be found", "Password": "Password", "Password confirmation": "Password confirmation", "PayPal ID": "PayPal ID", "PayPal ID is not set!": "PayPal ID is not set!", "PayPal automatically payout": "PayPal automatically payout", "PayPal payout info": "PayPal payout info", "Payment": "Payment", "Payment Method": "Payment Method", "Payment Type": "Payment Type", "Payment description": "Payment description", "Payment failed!": "Payment failed!", "Payment method": "Payment method", "Payment status": "Payment status", "Payment with :paymentType": "Payment with :paymentType", "Payout account": "Payout account", "Payout info": "Payout info", "Payout method is not accepted!": "Payout method is not accepted!", "Phone": "Phone", "Phone (optional)": "Phone (optional)", "Phone :number": "Phone :number", "Phone Number": "Phone Number", "Phone number": "Phone number", "Pinterest": "Pinterest", "Please <a href=\":link\">login</a> to write review!": "Please <a href=\":link\">login</a> to write review!", "Please fill out all shipping information to view available shipping methods!": "Please fill out all shipping information to view available shipping methods!", "Please provide a reason for the cancellation.": "Please provide a reason for the cancellation.", "Please purchase the product for a review!": "Please purchase the product for a review!", "Please select at least 1 product to return!": "Please select at least 1 product to return!", "Please select attributes": "Please select attributes", "Please select product options!": "Please select product options!", "Please solve the following math function: :label = ?": "Please solve the following math function: :label = ?", "Please switch currency to any supported currency": "Please switch currency to any supported currency", "Please try again in a few minutes, or alternatively return to the homepage by <a href=\":link\">clicking here</a>.": "Please try again in a few minutes, or alternatively return to the homepage by <a href=\":link\">clicking here</a>.", "Please wait for the administrator to review and approve!": "Please wait for the administrator to review and approve!", "Plus": "Plus", "Popular": "Popular", "Popular tags": "Popular tags", "Popup delay (seconds)": "Popup delay (seconds)", "Popup description": "Popup description", "Popup image": "Popup image", "Popup subtitle": "Popup subtitle", "Popup title": "Popup title", "Preloader Version": "Preloader Version", "Price": "Price", "Price Filter": "Price Filter", "Price:": "Price:", "Price: high to low": "Price: high to low", "Price: low to high": "Price: low to high", "Primary": "Primary", "Primary button background color": "Primary button background color", "Primary button color": "Primary button color", "Primary color": "Primary color", "Primary font": "Primary font", "Print invoice": "Print invoice", "Proceed to Checkout": "Proceed to Checkout", "Proceed to checkout": "Proceed to checkout", "Process payout": "Process payout", "Processed PayPal payout successfully!": "Processed PayPal payout successfully!", "Processing. Please wait...": "Processing. Please wait...", "Product": "Product", "Product :product is out of stock!": "Product :product is out of stock!", "Product :product limited quantity allowed is :quantity": "Product :product limited quantity allowed is :quantity", "Product Categories": "Product Categories", "Product Collections": "Product Collections", "Product FAQs": "Product FAQs", "Product ID is required": "Product ID is required", "Product Name": "Product Name", "Product Reviews": "Product Reviews", "Product category products": "Product category products", "Product detail sidebar": "Product detail sidebar", "Product is not published yet.": "Product is not published yet.", "Product name \":name\" does not exists": "Product name \":name\" does not exists", "Product(s)": "Product(s)", "Products": "Products", "Products found": "Products found", "Products list sidebar": "Products list sidebar", "Profile": "Profile", "Promotion": "Promotion", "Promotion discount amount": "Promotion discount amount", "Public Key": "Public Key", "Quantity": "Quantity", "Quantity is required!": "Quantity is required!", "Quantity must be a number!": "Quantity must be a number!", "Questions & Answers": "Questions & Answers", "Quick view": "Quick view", "Rating": "Rating", "Rating: high to low": "Rating: high to low", "Rating: low to high": "Rating: low to high", "Reason": "Reason", "Reason (optional)": "Reason (optional)", "Recent": "Recent", "Recent Orders": "Recent Orders", "Recent Post": "Recent Post", "Recent Posts": "Recent Posts", "Recently Viewed": "Recently Viewed", "Recently Viewed Products is a function which helps you keep track of your recent viewing history.": "Recently Viewed Products is a function which helps you keep track of your recent viewing history.", "Refund amount": "Refund amount", "Register": "Register", "Register an account": "Register an account", "Register an account on :name": "Register an account on :name", "Register an account with above information?": "Register an account with above information?", "Register as": "Register as", "Register now": "Register now", "Registered successfully!": "Registered successfully!", "Related Posts": "Related Posts", "Related products": "Related products", "Remember me": "Remember me", "Remove": "Remove", "Remove image": "Remove image", "Remove this item": "Remove this item", "Removed coupon :code successfully!": "Removed coupon :code successfully!", "Removed item from cart successfully!": "Removed item from cart successfully!", "Removed product :product from compare list successfully!": "Removed product :product from compare list successfully!", "Removed product :product from wishlist successfully!": "Removed product :product from wishlist successfully!", "Removing...": "Removing...", "Request": "Request", "Request Return Product(s)": "Request Return Product(s)", "Request Return Product(s) In Order :id": "Request Return Product(s) In Order :id", "Request delete account": "Request delete account", "Request number": "Request number", "Request return order with reason: :reason": "Request return order with reason: :reason", "Requires company invoice (Please fill in your company information to receive the invoice)?": "Requires company invoice (Please fill in your company information to receive the invoice)?", "Reset Password": "Reset Password", "Return Product(s)": "Return Product(s)", "Return Reason": "Return Reason", "Return items": "Return items", "Returned Goods": "Returned Goods", "Returned to Sender": "Returned to <PERSON>er", "Revenue": "Revenue", "Revenues": "Revenues", "Revenues in :label": "Revenues in :label", "Review": "Review", "Review product \":product\"": "Review product \":product\"", "Reviewed": "Reviewed", "Reviews": "Reviews", "Reviews :number": "Reviews :number", "Run": "Run", "SKU": "SKU", "SKU:": "SKU:", "Sales Reports": "Sales Reports", "Same as shipping information": "Same as shipping information", "Same fee :amount": "Same fee :amount", "Save settings": "Save settings", "Search": "Search", "Search blog posts": "Search blog posts", "Search for": "Search for", "Search for Products...": "Search for Products...", "Search in this store...": "Search in this store...", "Search result for \":query\"": "Search result for \":query\"", "Search result for: \":query\"": "Search result for: \":query\"", "Search store...": "Search store...", "Search...": "Search...", "Secret": "Secret", "Secret Key": "Secret Key", "Select :name": "Select :name", "Select Ads": "Select Ads", "Select Options": "Select Options", "Select a flash sale": "Select a flash sale", "Select a product collection": "Select a product collection", "Select available addresses": "Select available addresses", "Select billing address...": "Select billing address...", "Select categories": "Select categories", "Select category": "Select category", "Select city...": "Select city...", "Select country...": "Select country...", "Select file": "Select file", "Select state...": "Select state...", "Select stores from the list": "Select stores from the list", "Send": "Send", "Send Message": "Send Message", "Send Password Reset Link": "Send Password Reset Link", "Send message": "Send message", "Send message successfully!": "Send message successfully!", "Sent at": "Sent at", "Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.": "Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.", "Set the height of the logo in pixels. The default value is 45px.": "Set the height of the logo in pixels. The default value is 45px.", "Settings": "Settings", "Setup license code": "Setup license code", "Share on :social": "Share on :social", "Shipment status": "Shipment status", "Shipments": "Shipments", "Shipping Company Name": "Shipping Company Name", "Shipping Information": "Shipping Information", "Shipping Label Created": "Shipping Label Created", "Shipping Status": "Shipping Status", "Shipping fee": "Shipping fee", "Shipping information": "Shipping information", "Shipping method": "Shipping method", "Shop Name": "Shop Name", "Shop Name is required.": "Shop Name is required.", "Shop Now": "Shop Now", "Shop Phone": "Shop Phone", "Shop Phone is required.": "Shop Phone is required.", "Shop URL": "Shop URL", "Shop URL is existing. Please choose another one!": "Shop URL is existing. Please choose another one!", "Shop URL is required.": "Shop URL is required.", "Shop by Category": "Shop by Category", "Shopping Cart": "Shopping Cart", "Show": "Show", "Show Contact form": "Show Contact form", "Show Mobile App Available": "Show Mobile App Available", "Show author name?": "Show author name?", "Site Copyright": "Site Copyright", "Site features": "Site features", "Site information": "Site information", "Size": "Size", "Slug": "Slug", "Social": "Social", "Social Links": "Social Links", "Social Sharing": "Social Sharing", "Social links": "Social links", "Social sharing buttons": "Social sharing buttons", "Sold": "Sold", "Sold By": "Sold By", "Sold by": "Sold by", "Sold out": "Sold out", "Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.": "Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.", "Something went wrong.": "Something went wrong.", "Sorry, we are doing some maintenance. Please check back soon.": "Sorry, we are doing some maintenance. Please check back soon.", "Sort by": "Sort by", "Star": "Star", "Start Shopping": "Start Shopping", "Start Your Daily Shopping with <span>Nest Mart</span>": "Start Your Daily Shopping with <span>Nest Mart</span>", "Started from": "Started from", "State": "State", "Status": "Status", "Stay connected:": "Stay connected:", "Stay home & get your daily <br />needs from our shop": "Stay home & get your daily <br />needs from our shop", "Sticky header content position?": "Sticky header content position?", "Stock status": "Stock status", "Store ID": "Store ID", "Store Name": "Store Name", "Store Password (API/Secret key)": "Store Password (API/Secret key)", "Store URL": "Store URL", "Stores": "Stores", "Stores List Layout": "Stores List Layout", "Style": "Style", "Sub Total": "Sub Total", "Sub amount": "Sub amount", "Subject": "Subject", "Submit": "Submit", "Submit Return Request": "Submit Return Request", "Submit Review": "Submit Review", "Subscribe": "Subscribe", "Subscribe to newsletter successfully!": "Subscribe to newsletter successfully!", "Subtitle": "Subtitle", "Subtitle :number": "Subtitle :number", "Subtotal": "Subtotal", "Success": "Success", "Support 24/7": "Support 24/7", "Tab #:number": "Tab #:number", "Tablet Image": "Tablet Image", "Tags": "Tags", "Take me home": "Take me home", "Tax": "Tax", "Tax ID": "Tax ID", "Tax ID:": "Tax ID:", "Tax info": "Tax info", "Tax information": "Tax information", "Telegram": "Telegram", "Temporarily down for maintenance": "Temporarily down for maintenance", "Term and Policy": "Term and Policy", "Terms and Privacy Policy": "Terms and Privacy Policy", "Text color": "Text color", "Thank you for purchasing our products!": "Thank you for purchasing our products!", "The .env file is not writable.": "The .env file is not writable.", "The balance is not enough for withdrawal": "The balance is not enough for withdrawal", "The debug mode has been disabled successfully.": "The debug mode has been disabled successfully.", "The debug mode is already disabled.": "The debug mode is already disabled.", "The font size in pixels (px). Default is :default": "The font size in pixels (px). Default is :default", "The given email address has not been confirmed. <a href=\":resend_link\">Resend confirmation link.</a>": "The given email address has not been confirmed. <a href=\":resend_link\">Resend confirmation link.</a>", "The minimum withdrawal amount is :amount": "The minimum withdrawal amount is :amount", "The order could not be found. Please try again or contact us if you need assistance.": "The order could not be found. Please try again or contact us if you need assistance.", "The order is currently being processed. For expedited processing, kindly upload a copy of your payment proof:": "The order is currently being processed. For expedited processing, kindly upload a copy of your payment proof:", "The page you are looking for could not be found.": "The page you are looking for could not be found.", "The selected :attribute is invalid.": "The selected :attribute is invalid.", "The system is up-to-date. There are no new versions to update!": "The system is up-to-date. There are no new versions to update!", "Theme ads": "Theme ads", "Theme emails": "Theme emails", "Theme options": "Theme options", "There is no data to display!": "There is no data to display!", "This action will permanently delete your account and all associated data and irreversible. Please be sure before proceeding.": "This action will permanently delete your account and all associated data and irreversible. Please be sure before proceeding.", "This credential is invalid Google Analytics credentials.": "This credential is invalid Google Analytics credentials.", "This file is not a valid JSON file.": "This file is not a valid JSON file.", "This image will be used as placeholder for lazy load images.": "This image will be used as placeholder for lazy load images.", "This product is not available.": "This product is not available.", "This product is out of stock or not exists!": "This product is out of stock or not exists!", "Time": "Time", "Times downloaded": "Times downloaded", "Title": "Title", "Title :number": "Title :number", "To show chat box on that website, please go to :link and add :domain to whitelist domains!": "To show chat box on that website, please go to :link and add :domain to whitelist domains!", "Top Selling Products": "Top Selling Products", "Top footer sidebar": "Top footer sidebar", "Top header background color": "Top header background color", "Top header text color": "Top header text color", "Total": "Total", "Total Amount": "Total Amount", "Total amount": "Total amount", "Total stores showing: :number": "Total stores showing: :number", "Track": "Track", "Tracking ID": "Tracking ID", "Tracking Link": "Tracking Link", "Transaction ID": "Transaction ID", "Transaction is already successfully completed!": "Transaction is already successfully completed!", "Transaction is successfully completed!": "Transaction is successfully completed!", "Type": "Type", "Type your message...": "Type your message...", "UPI ID": "UPI ID", "URL": "URL", "Unable to set debug mode. No APP_DEBUG variable was found in the .env file.": "Unable to set debug mode. No APP_DEBUG variable was found in the .env file.", "Unit Price": "Unit Price", "Unknown": "Unknown", "Unlimited by default": "Unlimited by default", "Unsubscribe to newsletter successfully": "Unsubscribe to newsletter successfully", "Update": "Update", "Update :name": "Update :name", "Update cart successfully!": "Update cart successfully!", "Update profile successfully!": "Update profile successfully!", "Update return order status to: :status": "Update return order status to: :status", "Update successfully!": "Update successfully!", "Update withdrawal request #:id": "Update withdrawal request #:id", "Updated avatar successfully!": "Updated avatar successfully!", "Upload": "Upload", "Upload Service Account JSON File": "Upload Service Account JSON File", "Upload photos": "Upload photos", "Uploaded proof successfully": "Uploaded proof successfully", "Use source assets from?": "Use source assets from?", "Use this address as default.": "Use this address as default.", "Using A Promo Code?": "Using A Promo Code?", "Using coupon code": "Using coupon code", "Validation Fail!": "Validation Fail!", "Variables": "Variables", "Vendor": "<PERSON><PERSON><PERSON>", "Vendor Dashboard": "Vendor Dashboard", "Vendor Info": "Vendor Info", "Vendor:": "Vendor:", "View": "View", "View All": "View All", "View Cart": "View Cart", "View Full Orders": "View Full Orders", "View Full Products": "View Full Products", "View Receipt:": "View Receipt:", "View all results": "View all results", "View full details": "View full details", "View withdrawal request #:id": "View withdrawal request #:id", "View your ads.txt here: :url": "View your ads.txt here: :url", "View your store": "View your store", "View your store <a href=\":url\">here</a>": "View your store <a href=\":url\">here</a>", "Viewing message #:id": "Viewing message #:id", "Visit Store": "Visit Store", "Waiting for approval": "Waiting for approval", "Waiting for your review": "Waiting for your review", "We can't find the page you're looking for.": "We can't find the page you're looking for.", "We have sent you an email to verify your email. Please check and confirm your email address!": "We have sent you an email to verify your email. Please check and confirm your email address!", "We sent you another confirmation email. You should receive it shortly.": "We sent you another confirmation email. You should receive it shortly.", "We will send you an email to confirm your account deletion. Once you confirm, your account will be deleted permanently.": "We will send you an email to confirm your account deletion. Once you confirm, your account will be deleted permanently.", "WhatsApp": "WhatsApp", "When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.": "When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.", "Widget display blog categories": "Widget display blog categories", "Widget display site information": "Widget display site information", "Widgets in bottom footer sidebar": "Widgets in bottom footer sidebar", "Widgets in footer sidebar": "Widgets in footer sidebar", "Widgets in the blog page": "Widgets in the blog page", "Widgets in the product detail page": "Widgets in the product detail page", "Widgets on header products list page": "Widgets on header products list page", "Width": "<PERSON><PERSON><PERSON>", "Wishlist": "Wishlist", "Withdrawal images": "Withdrawal images", "Withdrawal request": "<PERSON><PERSON><PERSON> request", "Withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "Working time": "Working time", "Write your message here": "Write your message here", "Write your review": "Write your review", "X (Twitter)": "X (Twitter)", "Yes": "Yes", "Yes, turn off": "Yes, turn off", "You can add up to 3 contact info boxes, to show is required Name and Address": "You can add up to 3 contact info boxes, to show is required Name and Address", "You can change it <a href=\":link\">here</a>": "You can change it <a href=\":link\">here</a>", "You can create your app in :link": "You can create your app in :link", "You can either": "You can either", "You can get fan page ID using this site :link": "You can get fan page ID using this site :link", "You can now download it by clicking the links below": "You can now download it by clicking the links below", "You can upload the following file types: jpg, jpeg, png, pdf and max file size is 2MB.": "You can upload the following file types: jpg, jpeg, png, pdf and max file size is 2MB.", "You can upload up to :total photos, each photo maximum size is :max kilobytes": "You can upload up to :total photos, each photo maximum size is :max kilobytes", "You can upload up to :total photos, each photo maximum size is :max kilobytes.": "You can upload up to :total photos, each photo maximum size is :max kilobytes.", "You cannot send a message to your own store.": "You cannot send a message to your own store.", "You do not have any products to review yet. Just shopping!": "You do not have any products to review yet. Just shopping!", "You have :total product(s) but no orders yet": "You have :total product(s) but no orders yet", "You have a coupon code?": "You have a coupon code?", "You have created a payment #:charge_id via :channel :time : :amount": "You have created a payment #:charge_id via :channel :time : :amount", "You have money!": "You have money!", "You have recovered from previous orders!": "You have recovered from previous orders!", "You have reviewed this product already!": "You have reviewed this product already!", "You have uploaded a copy of your payment proof.": "You have uploaded a copy of your payment proof.", "You received a payment. Thanks for selling on our site!": "You received a payment. Thanks for selling on our site!", "You successfully confirmed your email address.": "You successfully confirmed your email address.", "You will be redirected to :name to complete the payment.": "You will be redirected to :name to complete the payment.", "You will receive money through the information below": "You will receive money through the information below", "YouTube URL": "YouTube URL", "YouTube video": "YouTube video", "Your Address": "Your Address", "Your Cart": "Your Cart", "Your Email": "Your Email", "Your Google Adsense ads.txt": "Your Google Adsense ads.txt", "Your Name": "Your Name", "Your Phone": "Your Phone", "Your account has been locked, please contact the administrator.": "Your account has been locked, please contact the administrator.", "Your asset files have been published successfully.": "Your asset files have been published successfully.", "Your cart is empty": "Your cart is empty", "Your cart is empty!": "Your cart is empty!", "Your compare list is empty": "Your compare list is empty", "Your email": "Your email", "Your email address": "Your email address", "Your email address will not be published. Required fields are marked *": "Your email address will not be published. Required fields are marked *", "Your email does not exist in the system or you have unsubscribed already!": "Your email does not exist in the system or you have unsubscribed already!", "Your email is in blacklist. Please use another email address.": "Your email is in blacklist. Please use another email address.", "Your email...": "Your email...", "Your full name": "Your full name", "Your message contains blacklist words: \":words\".": "Your message contains blacklist words: \":words\".", "Your name": "Your name", "Your order is successfully placed": "Your order is successfully placed", "Your personal data will be used to support your experience throughout this website, to manage access to your account.": "Your personal data will be used to support your experience throughout this website, to manage access to your account.", "Your rating:": "Your rating:", "Your shopping cart has digital product(s), so you need to sign in to continue!": "Your shopping cart has digital product(s), so you need to sign in to continue!", "Your system has been cleaned up successfully.": "Your system has been cleaned up successfully.", "Your wishlist list is empty": "Your wishlist list is empty", "Zip code": "Zip code", "Zipcode": "Zipcode", "billion": "billion", "by": "by", "centimeters": "centimeters", "days": "days", "error": "error", "for all orders": "for all orders", "for all product in collection :collections": "for all product in collection :collections", "for all products in category :categories": "for all products in category :categories", "for all products in collection :collections": "for all products in collection :collections", "for all products in order": "for all products in order", "for customer(s) :customers": "for customer(s) :customers", "for order with amount from :price": "for order with amount from :price", "for product variant(s) :variants": "for product variant(s) :variants", "for product(s) :products": "for product(s) :products", "grams": "grams", "here": "here", "hours": "hours", "in": "in", "kilograms": "kilograms", "limited to use coupon code per customer. This coupon can only be used once per customer!": "limited to use coupon code per customer. This coupon can only be used once per customer!", "meters": "meters", "million": "million", "mins": "mins", "on": "on", "or search for something else.": "or search for something else.", "return to the previous page": "return to the previous page", "secs": "secs", "show less": "show less", "show more": "show more", "visit our home page": "visit our home page", "when shipping fee less than or equal :amount": "when shipping fee less than or equal :amount", "x:quantity": "x:quantity", "© :year Your Company. All right reserved.": "© :year Your Company. All right reserved.", "✅ Purchased :time": "✅ Purchased :time"}