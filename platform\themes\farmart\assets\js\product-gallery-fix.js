/**
 * Product Gallery Image Stacking Fix - Complete Rebuild
 * Prevents images from stacking before slick carousel initialization
 */

(function($) {
    'use strict';

    // Configuration
    const GALLERY_SELECTORS = [
        '.bb-product-gallery-images',
        '.bb-quick-view-gallery-images', 
        '.product-gallery__wrapper',
        '.slick-slides-carousel'
    ];

    const IMAGE_SELECTORS = [
        '.bb-product-gallery-images > a',
        '.bb-quick-view-gallery-images > a',
        '.product-gallery__wrapper > a',
        '.slick-slides-carousel > *'
    ];

    let initialized = false;
    let observers = [];

    /**
     * Initialize the gallery fix
     */
    function initGalleryFix() {
        if (initialized) return;
        initialized = true;

        console.log('Initializing Product Gallery Fix...');

        // Add loading class to body
        $('body').addClass('gallery-loading');

        // Process existing galleries
        processExistingGalleries();

        // Set up mutation observer for dynamic content
        setupMutationObserver();

        // Set up slick event listeners
        setupSlickEventListeners();

        // Cleanup after a delay
        setTimeout(function() {
            $('body').removeClass('gallery-loading');
        }, 2000);
    }

    /**
     * Process galleries that already exist on page load
     */
    function processExistingGalleries() {
        GALLERY_SELECTORS.forEach(function(selector) {
            $(selector).each(function() {
                processGallery($(this));
            });
        });
    }

    /**
     * Process a single gallery element
     */
    function processGallery($gallery) {
        if ($gallery.hasClass('gallery-processed')) return;
        
        console.log('Processing gallery:', $gallery[0]);
        
        $gallery.addClass('gallery-processed gallery-loading');

        // Hide all images except first
        hideExtraImages($gallery);

        // Check if slick is already initialized
        if ($gallery.hasClass('slick-initialized')) {
            handleSlickInitialized($gallery);
        } else {
            // Wait for slick initialization
            waitForSlickInitialization($gallery);
        }
    }

    /**
     * Hide all images except the first one
     */
    function hideExtraImages($gallery) {
        const $images = $gallery.find('> a, > *').not(':first-child');
        
        $images.each(function() {
            const $img = $(this);
            $img.css({
                'display': 'none',
                'visibility': 'hidden',
                'opacity': '0',
                'position': 'absolute',
                'left': '-10000px',
                'top': '-10000px'
            });
        });

        // Ensure first image is visible
        const $firstImage = $gallery.find('> a:first-child, > *:first-child');
        $firstImage.css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'position': 'relative',
            'left': 'auto',
            'top': 'auto'
        });
    }

    /**
     * Wait for slick initialization with timeout
     */
    function waitForSlickInitialization($gallery) {
        let attempts = 0;
        const maxAttempts = 50; // 5 seconds max wait
        
        const checkInterval = setInterval(function() {
            attempts++;
            
            if ($gallery.hasClass('slick-initialized')) {
                clearInterval(checkInterval);
                handleSlickInitialized($gallery);
            } else if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                console.warn('Slick initialization timeout for gallery:', $gallery[0]);
                // Fallback: show all images
                showAllImages($gallery);
            }
        }, 100);
    }

    /**
     * Handle when slick is initialized
     */
    function handleSlickInitialized($gallery) {
        console.log('Slick initialized for gallery:', $gallery[0]);
        
        setTimeout(function() {
            showAllImages($gallery);
            $gallery.removeClass('gallery-loading').addClass('gallery-ready');
        }, 100);
    }

    /**
     * Show all images (called after slick initialization)
     */
    function showAllImages($gallery) {
        const $images = $gallery.find('> a, > *, .slick-slide');
        
        $images.each(function() {
            const $img = $(this);
            $img.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1',
                'position': 'relative',
                'left': 'auto',
                'top': 'auto'
            });
        });
    }

    /**
     * Set up mutation observer for dynamic content
     */
    function setupMutationObserver() {
        if (typeof MutationObserver === 'undefined') return;

        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const $node = $(node);
                        
                        // Check if the added node is a gallery
                        GALLERY_SELECTORS.forEach(function(selector) {
                            if ($node.is(selector)) {
                                processGallery($node);
                            }
                            
                            // Check for galleries within the added node
                            $node.find(selector).each(function() {
                                processGallery($(this));
                            });
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        observers.push(observer);
    }

    /**
     * Set up slick event listeners
     */
    function setupSlickEventListeners() {
        // Listen for slick init events
        $(document).on('init', GALLERY_SELECTORS.join(', '), function(event, slick) {
            const $gallery = $(this);
            console.log('Slick init event for:', $gallery[0]);
            handleSlickInitialized($gallery);
        });

        // Listen for slick afterChange events
        $(document).on('afterChange', GALLERY_SELECTORS.join(', '), function(event, slick, currentSlide) {
            const $gallery = $(this);
            if (!$gallery.hasClass('gallery-ready')) {
                handleSlickInitialized($gallery);
            }
        });
    }

    /**
     * Cleanup function
     */
    function cleanup() {
        observers.forEach(function(observer) {
            observer.disconnect();
        });
        observers = [];
        initialized = false;
    }

    // Initialize when DOM is ready
    $(document).ready(function() {
        initGalleryFix();
    });

    // Re-initialize on AJAX complete (for dynamic content)
    $(document).ajaxComplete(function() {
        setTimeout(function() {
            processExistingGalleries();
        }, 100);
    });

    // Handle page visibility change
    $(document).on('visibilitychange', function() {
        if (!document.hidden) {
            setTimeout(function() {
                processExistingGalleries();
            }, 500);
        }
    });

    // Expose cleanup function globally
    window.cleanupGalleryFix = cleanup;

})(jQuery);
