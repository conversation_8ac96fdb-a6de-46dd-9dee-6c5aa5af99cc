<!-- Mobile style to hide quantity and buttons and style product options -->
<style>
    @media (max-width: 992px) {
        .cart-form .quantity,
        .cart-form .product-button {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            overflow: hidden !important;
        }



        /* Modern styling for product options section */
        .pr_switch_wrap {
            border: 2px solid #ff6633;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            position: relative;
            background-color: #fff;
            box-shadow: 0 4px 10px rgba(255, 102, 51, 0.1);
        }

        /* Only add the title to the first product options section */
        .cart-form .pr_switch_wrap:first-of-type::before {
            content: "Product options";
            position: absolute;
            top: -12px;
            left: 15px;
            background-color: #fff;
            padding: 0 10px;
            font-weight: 600;
            font-size: 16px;
            color: #ff6633;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Style for color options */
        .attribute-swatches-wrapper .attribute-swatch {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 102, 51, 0.1);
        }

        .attribute-swatches-wrapper .attribute-swatch:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .attribute-swatches-wrapper .attribute-name {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            font-size: 15px;
        }

        .attribute-swatches-wrapper .attribute-values {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        /* Fix for attribute lists */
        .attribute-swatches-wrapper ul {
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 10px !important;
            padding: 0 !important;
            margin: 0 !important;
            list-style: none !important;
        }

        .attribute-swatches-wrapper li {
            display: inline-block !important;
            margin: 0 !important;
        }

        /* Fix for inputs */
        .attribute-swatches-wrapper input[type="radio"],
        .attribute-swatches-wrapper input[type="checkbox"] {
            position: absolute !important;
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
        }

        /* Style for size options */
        .attribute-swatches-wrapper .text-swatch li label {
            display: block !important;
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            padding: 8px 15px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            min-width: 45px !important;
            text-align: center !important;
            font-weight: 500 !important;
            margin: 0 !important;
        }

        .attribute-swatches-wrapper .text-swatch li label:hover {
            border-color: #ff6633 !important;
        }

        .attribute-swatches-wrapper .text-swatch li input:checked ~ label {
            background-color: #ff6633 !important;
            border-color: #ff6633 !important;
            color: #fff !important;
        }

        /* Style for color swatches */
        .attribute-swatches-wrapper .visual-swatch li label {
            display: block !important;
            width: 30px !important;
            height: 30px !important;
            border-radius: 50% !important;
            overflow: hidden !important;
            border: 1px solid #ddd !important;
            position: relative !important;
            margin: 0 !important;
        }

        .attribute-swatches-wrapper .visual-swatch li label span {
            display: block !important;
            width: 100% !important;
            height: 100% !important;
        }

        .attribute-swatches-wrapper .visual-swatch li input:checked ~ label {
            border: 2px solid #ff6633 !important;
            transform: scale(1.1) !important;
            box-shadow: 0 2px 8px rgba(255, 102, 51, 0.3) !important;
        }

        /* Ripple effect for option selections */
        .attribute-swatches-wrapper label {
            position: relative !important;
            overflow: hidden !important;
        }

        .ripple-effect {
            position: absolute !important;
            border-radius: 50% !important;
            background-color: rgba(255, 102, 51, 0.3) !important;
            width: 100px !important;
            height: 100px !important;
            margin-top: -50px !important;
            margin-left: -50px !important;
            animation: ripple-animation 0.6s !important;
            opacity: 0 !important;
            pointer-events: none !important;
            transform: scale(0) !important;
            top: 50% !important;
            left: 50% !important;
            z-index: 1 !important;
        }

        @keyframes ripple-animation {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(3);
                opacity: 0;
            }
        }

        /* Fix for dropdown swatches */
        .attribute-swatches-wrapper .dropdown-swatches-wrapper select {
            display: block !important;
            width: 100% !important;
            height: 40px !important;
            padding: 5px 10px !important;
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            background-color: #fff !important;
            color: #333 !important;
            font-size: 14px !important;
        }

        .attribute-swatches-wrapper .dropdown-swatches-wrapper select:focus {
            border-color: #ff6633 !important;
            outline: none !important;
        }
    }

    /* Hide original product buttons on desktop since we have AliExpress-style cart in sidebar */
    @media (min-width: 993px) {
        .cart-form .product-button {
            display: none !important;
        }
    }
</style>
<form
    class="cart-form"
    action="{{ route('public.cart.add-to-cart') }}"
    method="POST"
>
    @csrf
    @if ((!empty($withVariations) && $product->variations()->count()) || (isset($withProductOptions) && $withProductOptions && $product->options()->count() > 0))
        <div class="pr_switch_wrap product-options-wrapper">
            @if (!empty($withVariations) && $product->variations()->count())
                {!! render_product_swatches($product, [
                    'selected' => $selectedAttrs,
                ]) !!}
            @endif

            @if (isset($withProductOptions) && $withProductOptions && $product->options()->count() > 0)
                {!! render_product_options($product) !!}
            @endif
        </div>
        <div
            class="number-items-available"
            style="display: none; margin-bottom: 10px;"
        ></div>
    @endif

    <input
        class="hidden-product-id"
        name="id"
        type="hidden"
        value="{{ $product->is_variation || !$product->defaultVariation->product_id ? $product->id : $product->defaultVariation->product_id }}"
    />

    @if (EcommerceHelper::isCartEnabled() || !empty($withButtons))
        {!! apply_filters(ECOMMERCE_PRODUCT_DETAIL_EXTRA_HTML, null, $product) !!}
        <div class="product-button">
            @if (EcommerceHelper::isCartEnabled())
                {!! Theme::partial('ecommerce.product-quantity', compact('product')) !!}
                <button
                    class="btn btn-primary mb-2 add-to-cart-button @if ($product->isOutOfStock()) disabled @endif"
                    name="add_to_cart"
                    type="submit"
                    value="1"
                    title="{{ __('Add to cart') }}"
                    @if ($product->isOutOfStock()) disabled @endif
                >
                    <span class="svg-icon">
                        <svg>
                            <use
                                href="#svg-icon-cart"
                                xlink:href="#svg-icon-cart"
                            ></use>
                        </svg>
                    </span>
                    <span class="add-to-cart-text ms-2">{{ __('Add to cart') }}</span>
                </button>

                @if (EcommerceHelper::isQuickBuyButtonEnabled() && isset($withBuyNow) && $withBuyNow)
                    <button
                        class="btn btn-primary btn-black mb-2 add-to-cart-button @if ($product->isOutOfStock()) disabled @endif"
                        name="checkout"
                        type="submit"
                        value="1"
                        title="{{ __('Buy Now') }}"
                        @if ($product->isOutOfStock()) disabled @endif
                    >
                        <span class="add-to-cart-text ms-2">{{ __('Buy Now') }}</span>
                    </button>
                @endif
            @endif
            @if (!empty($withButtons))
                {!! Theme::partial('ecommerce.product-loop-buttons', compact('product', 'wishlistIds')) !!}
            @endif
        </div>


    @endif
</form>


