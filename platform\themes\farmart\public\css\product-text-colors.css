/* Product title color */
.product__title a,
.product-inner .product__title a,
.product-details .product__title a,
h3.product__title a,
.product-content-box .product__title a,
body .product__title a {
    color: #000000 !important;
}

.product__title a:hover,
.product-inner .product__title a:hover,
.product-details .product__title a:hover,
h3.product__title a:hover,
.product-content-box .product__title a:hover,
body .product__title a:hover {
    color: #ff6633 !important;
}

/* Vendor name color and styling */
.sold-by-meta a,
.product-inner .sold-by-meta a,
.product-details .sold-by-meta a,
.product-content-box .sold-by-meta a,
body .sold-by-meta a {
    color: #000080 !important; /* Navy blue */
    border: 1px solid #000080 !important;
    border-radius: 4px !important;
    padding: 2px 8px !important;
    display: inline-block !important;
    margin-bottom: 5px !important;
    text-decoration: none !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    background-color: rgba(0, 0, 128, 0.05) !important;
}

.sold-by-meta a:hover,
.product-inner .sold-by-meta a:hover,
.product-details .sold-by-meta a:hover,
.product-content-box .sold-by-meta a:hover,
body .sold-by-meta a:hover {
    color: #ff6633 !important;
    border-color: #ff6633 !important;
    background-color: rgba(255, 102, 51, 0.05) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Additional direct styling */
a[href*="products/"] {
    color: #000000 !important;
}

a[href*="stores/"] {
    color: #000080 !important;
}
