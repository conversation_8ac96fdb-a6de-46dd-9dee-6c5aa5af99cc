.header--dashboard {
    .header__site-link {
        i {
            margin-left: 0;
            margin-right: 1em;

            &:before {
                content: '\e8fd';
            }
        }
    }
}

.ps-block--stat {
    .ps-block__content {
        padding-left: 0;
        padding-right: 28px;
    }
}

.menu {
    > li {
        > a {
            i {
                margin-right: 0;
                margin-left: 30px;
            }

            &:after {
                right: auto;
                left: 0;
            }
        }
    }
}

.ps-block--user-wellcome {
    .ps-block__right {
        padding-left: 0;
        padding-right: 10px;
    }
}

.ps-block__action {
    i {
        &:before {
            content: '\e8fd';
        }
    }
}

.ps-sidebar {
    .ps-sidebar__top {
        padding-right: 0;
        padding-left: 30px;
    }
}

@media screen and (min-width: 1680px) {
    .ps-dashboard {
        .ps-section__right {
            padding-left: 0;
            padding-right: 80px;
        }

        .ps-section__left {
            padding-right: 0;
            padding-left: 75px;
        }
    }

    .ps-sidebar {
        .ps-sidebar__top {
            padding-right: 0;
            padding-left: 60px;
        }
    }
}

@media screen and (min-width: 1200px) {
    .ps-dashboard {
        .ps-section__left {
            padding-right: 0;
            padding-left: 30px;
        }

        .ps-section__right {
            padding-left: 0;
            padding-right: 30px;
            border-left: none;
            border-right: 1px solid #e6e6e6;
        }
    }

    .ps-main {
        .ps-main__sidebar {
            padding: 60px 60px 60px 0;
        }
    }
}
