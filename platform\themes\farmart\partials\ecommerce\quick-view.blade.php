<div class="container product-detail-container">
    <div class="row">
        <div class="col-md-6">
            <div class="px-1 py-1 px-lg-5 py-lg-5 py-md-1 my-lg-5 my-md-1 my-2 mb-4 position-relative">
                <div class="bb-quick-view-gallery-images position-relative">
                    @foreach ($productImages as $image)
                        <a href="{{ RvMedia::getImageUrl($image) }}">
                            {{ RvMedia::image($image, $product->name, 'medium') }}
                        </a>
                    @endforeach

                    <!-- Image Counter -->
                    <div class="d-lg-none" style="position: absolute; bottom: 15px; right: 15px; background-color: rgba(0, 0, 0, 0.6); color: #fff; padding: 5px 10px; border-radius: 15px; font-size: 12px; z-index: 10;">
                        <span class="current-image">1</span>/<span class="total-images">{{ count($productImages) }}</span>
                    </div>

                    <!-- Navigation Arrows -->
                    <div class="d-lg-none" style="position: absolute; top: 50%; left: 10px; transform: translateY(-50%); z-index: 10;">
                        <button class="btn-gallery-nav btn-prev" style="background-color: rgba(255, 255, 255, 0.7); border: none; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                            <span class="svg-icon" style="width: 20px; height: 20px;">
                                <svg>
                                    <use href="#svg-icon-chevron-left" xlink:href="#svg-icon-chevron-left"></use>
                                </svg>
                            </span>
                        </button>
                    </div>
                    <div class="d-lg-none" style="position: absolute; top: 50%; right: 10px; transform: translateY(-50%); z-index: 10;">
                        <button class="btn-gallery-nav btn-next" style="background-color: rgba(255, 255, 255, 0.7); border: none; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                            <span class="svg-icon" style="width: 20px; height: 20px;">
                                <svg>
                                    <use href="#svg-icon-chevron-right" xlink:href="#svg-icon-chevron-right"></use>
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
                @if (EcommerceHelper::isWishlistEnabled())
                    <div class="wishlist-button product-wishlist-button" style="position: absolute; top: 15px; right: 15px; z-index: 10;">
                        <a
                            class="wishlist btn btn-sm rounded-circle @if (!empty($wishlistIds) && in_array($product->id, $wishlistIds)) added-to-wishlist @endif"
                            data-url="{{ route('public.wishlist.add', $product->id) }}"
                            href="#"
                            title="{{ __('Add to Wishlist') }}"
                            style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border: none; background-color: rgba(0, 0, 0, 0.15); color: #fff; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); position: relative;"
                        >
                            <span class="svg-icon" style="width: 20px; height: 20px;">
                                <svg>
                                    <use
                                        href="#svg-icon-wishlist"
                                        xlink:href="#svg-icon-wishlist"
                                    ></use>
                                </svg>
                            </span>
                        </a>
                    </div>
                @endif
            </div>
        </div>
        <div class="col-md-6">
            <div class="product-modal-entry product-details js-product-content">
                <div class="entry-product-header">
                    <div class="product-header-left">
                        <h2 class="h3 product_title entry-title"><a href="{{ $product->url }}">{{ $product->name }}</a>
                        </h2>
                        <div class="product-entry-meta">
                            @if ($product->brand_id)
                                <p class="mb-0 me-2 pe-2 text-secondary">{{ __('Brand') }}: <a
                                        href="{{ $product->brand->url }}"
                                    >{{ $product->brand->name }}</a></p>
                            @endif

                            @if (EcommerceHelper::isReviewEnabled())
                                <div class="col-auto">
                                    {!! Theme::partial('star-rating', ['avg' => $product->reviews_avg, 'count' => $product->reviews_count]) !!}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                {!! Theme::partial('ecommerce.product-price', compact('product')) !!}

                @if (is_plugin_active('marketplace') && $product->store_id)
                    <div class="product-meta-sold-by my-2">
                        <span class="d-inline-block">{{ __('Sold By') }}: </span>
                        <a href="{{ $product->store->url }}">
                            {{ $product->store->name }}
                        </a>
                    </div>
                @endif

                {!! Theme::partial('ecommerce.product-availability', compact('product', 'productVariation')) !!}

                <div class="product-details__short-description">
                    {!! apply_filters('ecommerce_before_product_description', null, $product) !!}
                    {!! BaseHelper::clean($product->description) !!}
                    {!! apply_filters('ecommerce_after_product_description', null, $product) !!}
                </div>
                {!! Theme::partial(
                    'ecommerce.product-cart-form',
                    compact('product', 'wishlistIds', 'selectedAttrs') + [
                        'withButtons' => true,
                        'withVariations' => true,
                        'withProductOptions' => true,
                        'withBuyNow' => true,
                    ],
                ) !!}

                <div class="meta-sku @if (!$product->sku) d-none @endif">
                    <span class="meta-label d-inline-block pe-2">{{ __('SKU') }}:</span>
                    <span class="meta-value">{{ $product->sku }}</span>
                </div>
                @if ($product->categories->isNotEmpty())
                    <div class="meta-categories">
                        <span class="meta-label d-inline-block pe-2">{{ __('Categories') }}:</span>
                        @foreach ($product->categories as $category)
                            <a href="{{ $category->url }}">{{ $category->name }}</a>
                            @if (!$loop->last)
                                ,
                            @endif
                        @endforeach
                    </div>
                @endif
                @if ($product->tags->isNotEmpty())
                    <div class="meta-categories">
                        <span class="meta-label d-inline-block pe-2">{{ __('Tags') }}:</span>
                        @foreach ($product->tags as $tag)
                            <a href="{{ $tag->url }}">{{ $tag->name }}</a>
                            @if (!$loop->last)
                                ,
                            @endif
                        @endforeach
                    </div>
                @endif

            </div>
            @if (theme_option('social_share_enabled', 'yes') == 'yes')
                <div class="mt-5">
                    {!! Theme::partial('share-socials', compact('product')) !!}
                </div>
            @endif
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gallery navigation for quick view
        setTimeout(function() {
            // Update the current image number when the slide changes
            $('.bb-quick-view-gallery-images').on('afterChange', function(event, slick, currentSlide) {
                $('.current-image').text(currentSlide + 1);
            });

            // Add click handlers for the navigation buttons
            $('.btn-prev').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-quick-view-gallery-images').slick('slickPrev');
            });

            $('.btn-next').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-quick-view-gallery-images').slick('slickNext');
            });
        }, 1000); // Wait 1 second for slick to initialize
    });
</script>
