/* Product Gallery Image Stacking Fix - Complete Rebuild */

/* 1. Hide all images except first by default to prevent stacking */
.bb-product-gallery-images > a:not(:first-child),
.bb-quick-view-gallery-images > a:not(:first-child),
.product-gallery__wrapper > a:not(:first-child),
.slick-slides-carousel > *:not(:first-child) {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* 2. Show first image always */
.bb-product-gallery-images > a:first-child,
.bb-quick-view-gallery-images > a:first-child,
.product-gallery__wrapper > a:first-child,
.slick-slides-carousel > *:first-child {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 3. Once slick is initialized, show all slides properly */
.bb-product-gallery-images.slick-initialized > a,
.bb-quick-view-gallery-images.slick-initialized > a,
.bb-product-gallery-images.slick-initialized .slick-slide,
.bb-quick-view-gallery-images.slick-initialized .slick-slide,
.product-gallery__wrapper.slick-initialized > a,
.product-gallery__wrapper.slick-initialized .slick-slide,
.slick-slides-carousel.slick-initialized > *,
.slick-slides-carousel.slick-initialized .slick-slide {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 4. Ensure proper positioning for gallery containers */
.bb-product-gallery-images,
.bb-quick-view-gallery-images,
.product-gallery__wrapper,
.slick-slides-carousel {
    position: relative !important;
    overflow: hidden !important;
}

/* 5. Prevent images from overlapping */
.bb-product-gallery-images > a,
.bb-quick-view-gallery-images > a,
.product-gallery__wrapper > a {
    position: relative !important;
    z-index: 1 !important;
}

/* 6. Slick slider specific fixes */
.slick-track {
    display: flex !important;
    align-items: center !important;
}

.slick-slide {
    float: none !important;
    display: block !important;
}

/* 7. Loading state - hide all images until slick is ready */
.bb-product-gallery-images:not(.slick-initialized):not(.gallery-ready),
.bb-quick-view-gallery-images:not(.slick-initialized):not(.gallery-ready),
.product-gallery__wrapper:not(.slick-initialized):not(.gallery-ready),
.slick-slides-carousel:not(.slick-initialized):not(.gallery-ready) {
    min-height: 300px;
}

.bb-product-gallery-images:not(.slick-initialized):not(.gallery-ready) > a:not(:first-child),
.bb-quick-view-gallery-images:not(.slick-initialized):not(.gallery-ready) > a:not(:first-child),
.product-gallery__wrapper:not(.slick-initialized):not(.gallery-ready) > a:not(:first-child),
.slick-slides-carousel:not(.slick-initialized):not(.gallery-ready) > *:not(:first-child) {
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    display: none !important;
}

/* 8. Smooth transition when slick initializes */
.bb-product-gallery-images.slick-initialized,
.bb-quick-view-gallery-images.slick-initialized,
.product-gallery__wrapper.slick-initialized,
.slick-slides-carousel.slick-initialized {
    transition: opacity 0.3s ease-in-out;
}

/* 9. Ensure images maintain aspect ratio */
.bb-product-gallery-images img,
.bb-quick-view-gallery-images img,
.product-gallery__wrapper img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
}

/* 10. Quick view specific fixes */
.bb-quick-view-content .bb-quick-view-gallery-images {
    position: relative !important;
}

.bb-quick-view-content .bb-quick-view-gallery-images > a {
    position: relative !important;
    display: block !important;
}

/* 11. Product page specific fixes */
.bb-product-gallery .bb-product-gallery-images {
    position: relative !important;
}

.bb-product-gallery .bb-product-gallery-images > a {
    position: relative !important;
    display: block !important;
}

/* 12. Force single image display until carousel is ready */
.gallery-loading .bb-product-gallery-images > a:not(:first-child),
.gallery-loading .bb-quick-view-gallery-images > a:not(:first-child),
.gallery-loading .product-gallery__wrapper > a:not(:first-child),
.gallery-loading .slick-slides-carousel > *:not(:first-child) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -10000px !important;
    top: -10000px !important;
}

/* 13. Loading indicator */
.gallery-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 14. Ensure proper cleanup after initialization */
.slick-initialized.gallery-ready > a,
.slick-initialized.gallery-ready .slick-slide {
    position: relative !important;
    left: auto !important;
    top: auto !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
