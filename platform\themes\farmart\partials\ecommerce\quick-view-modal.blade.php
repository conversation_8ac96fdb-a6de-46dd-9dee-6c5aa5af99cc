<div
    class="modal fade"
    id="product-quick-view-modal"
    aria-labelledby="product-quick-view-label"
    aria-hidden="true"
    tabindex="-1"
>
    <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered modal-xl">
        <div class="modal-content position-relative">
            <!-- Enhanced close button -->
            <button
                class="btn-close"
                data-bs-dismiss="modal"
                type="button"
                aria-label="Close"
                style="z-index: 99999; position: absolute; top: 20px; right: 20px; opacity: 1; background-color: white; border: 1px solid #ccc; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;"
                onclick="closeQuickViewModal(event)"
            ></button>

            <script>
            function closeQuickViewModal(event) {
                event.preventDefault();
                event.stopPropagation();

                // Get the modal
                var modal = document.getElementById('product-quick-view-modal');

                // Try multiple methods to close it
                // Method 1: Bootstrap
                if (typeof bootstrap !== 'undefined') {
                    var bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) bsModal.hide();
                }

                // Method 2: jQuery
                if (typeof jQuery !== 'undefined') {
                    jQuery(modal).modal('hide');
                }

                // Method 3: Direct DOM
                modal.classList.remove('show');
                modal.style.display = 'none';
                document.body.classList.remove('modal-open');

                // Remove backdrop
                var backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(function(backdrop) {
                    backdrop.remove();
                });
            }
            </script>
            <div class="modal-body">
                <div class="product-modal-content py-5">

                </div>
            </div>
        </div>
    </div>
    <div class="modal-loading"></div>
</div>
