.cart--mini {
    position: relative;
    display: block;

    .header-item-counter {
        top: 0;
    }

    .cart__content {
        position: absolute;
        min-width: 400px;
        right: -0;
        top: 100%;
        z-index: 30;
        padding-top: 10px;
        @include transform(translate(0, 20px));
        transition: all .4s cubic-bezier(0.4, 0, 0.2, 1);
        visibility: hidden;
        opacity: 0;
        display: none;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .cart__footer {
        padding: 20px 24px 24px;
        background-color: #ffffff;
        border: none;
        border-radius: 0 0 12px 12px;

        h3 {
            display: block;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;

            strong {
                float: right;
                color: #ef4444;
            }
        }
    }

    figure {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        align-items: center;

        .btn {
            padding: 12px 25px;
        }
    }

    .mini-cart-content {
        .widget-shopping-cart-content {
            border: none;
            background-color: #fff;
            border-radius: 12px;
            overflow: hidden;

            .control-buttons {
                border: none !important;
                background: none !important;
                background-color: transparent !important;

                &::before, &::after {
                    display: none !important;
                }

                .mini-cart__buttons {
                    border: none !important;
                    background: none !important;
                    background-color: transparent !important;

                    &.row {
                        border: none !important;
                        background: none !important;
                        background-color: transparent !important;

                        &::before, &::after {
                            display: none !important;
                        }
                    }
                }
            }
        }

        ul {
            &.mini-product-cart-list {
                border-radius: 12px 12px 0 0;
                max-height: 100vh;
                overflow-y: auto;
                overflow-x: hidden;
                padding: 0;
                margin: 0;

                &::-webkit-scrollbar {
                    background: 0 0;
                    width: 6px;
                }

                &::-webkit-scrollbar-thumb {
                    background-color: #e5e7eb;
                    border-radius: 3px;
                }

                &::-webkit-scrollbar-thumb:hover {
                    background-color: #d1d5db;
                }

                li {
                    &.mini-cart-item {
                        list-style: none;
                        display: flex;
                        position: relative;
                        padding: 16px 20px;
                        margin: 0;
                        border-bottom: 1px solid #f3f4f6;
                        transition: background-color 0.2s ease;
                        align-items: flex-start;
                        gap: 12px;

                        &:hover {
                            background-color: #f9fafb;
                        }

                        &:last-child {
                            border-bottom: none;
                        }

                        .product-image {
                            flex-shrink: 0;
                            width: 64px;
                            height: 64px;
                            border-radius: 8px;
                            overflow: hidden;
                            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);

                            img {
                                padding: 0;
                                border-radius: 8px;
                                object-fit: cover;
                                width: 100%;
                                height: 100%;
                            }
                        }

                        .product-content {
                            flex: 1;
                            min-width: 0;

                            .product-attributes {
                                margin-top: 4px;

                                small {
                                    color: #6b7280;
                                    font-size: 12px;
                                }
                            }

                            .product-options {
                                margin-top: 4px;
                                font-size: 12px;
                                color: #6b7280;
                            }

                            .qty-text {
                                margin-left: 4px;
                                color: #6b7280;
                            }
                        }

                        .remove-item {
                            flex-shrink: 0;
                            margin-left: 8px;

                            .remove-cart-item {
                                width: 32px;
                                height: 32px;
                                border-radius: 6px;
                                background-color: #f3f4f6;
                                border: none;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                transition: all 0.2s ease;
                                padding: 0;

                                &:hover {
                                    background-color: #fee2e2;
                                    transform: scale(1.05);
                                }

                                svg {
                                    width: 14px;
                                    height: 14px;
                                    color: #6b7280;
                                }

                                &:hover svg {
                                    color: #dc2626;
                                }
                            }
                        }
                    }
                }
            }
        }

        .cart_no_items {
            padding: 40px 20px;
            text-align: center;

            .empty-cart-content {
                .empty-cart-icon {
                    margin-bottom: 16px;

                    svg {
                        color: #d1d5db;
                    }
                }

                .empty-cart-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 8px;
                }

                .empty-cart-message {
                    font-size: 14px;
                    color: #6b7280;
                    margin: 0;
                }
            }
        }

        .product-content {
            padding-right: 50px;
            flex: 1;

            .product-image {
                flex: none;
                width: 64px;
                height: 64px;
                margin-right: 16px;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            }

            .product-name {
                margin-bottom: 8px;
                font-size: 14px;
                font-weight: 500;

                a {
                    line-height: 1.4;
                    color: #111827;
                    text-decoration: none;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;

                    &:hover {
                        color: #26901b;
                    }
                }
            }

            .product-vendor {
                margin-bottom: 6px;

                a {
                    font-size: 12px;
                    color: #6b7280;
                    text-decoration: none;

                    &:hover {
                        color: #26901b;
                    }
                }
            }
        }

        .quantity {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 4px;

            .price-amount {
                font-weight: 600;
                color: #111827;
                font-size: 14px;

                small {
                    margin-left: 6px;

                    del {
                        color: #9ca3af;
                        font-size: 12px;
                    }
                }
            }
        }

        .control-buttons,
        .cart-buttons-only {
            padding: 20px 24px 24px;
            border-top: none !important;
            background-color: transparent !important;
            background: none !important;
            border-radius: 0 0 12px 12px;
            border: none !important;
        }

        .cart-buttons-only {
            border: none !important;
            background: none !important;
            background-color: transparent !important;

            &::before, &::after {
                display: none !important;
            }
        }

        // Force proper styling after AJAX updates
        .panel__header {
            padding: 20px 24px !important;
            border-bottom: 1px solid #f3f4f6 !important;
            background-color: #fafafa !important;
            position: relative !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;

            h3 {
                font-size: 18px !important;
                font-weight: 600 !important;
                color: #111827 !important;
                margin: 0 !important;
                flex: 1 !important;
                text-align: left !important;

                .cart-counter {
                    color: #6b7280 !important;
                    font-weight: 500 !important;
                }
            }

            .close-toggle--sidebar {
                position: static !important;
                right: auto !important;
                top: auto !important;
                transform: none !important;
                width: 32px !important;
                height: 32px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                border-radius: 6px !important;
                background-color: #f3f4f6 !important;
                transition: all 0.2s ease !important;
                flex-shrink: 0 !important;
                color: #6b7280 !important;
                font-size: 16px !important;
                padding: 0 !important;

                &:hover {
                    background-color: #e5e7eb !important;
                    color: #374151 !important;
                }

                svg {
                    width: 16px !important;
                    height: 16px !important;
                    color: inherit !important;
                }
            }
        }

        .mini-cart__total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            color: #111827;
            padding: 12px 0;
            font-size: 15px;

            &:last-of-type {
                border-top: 1px solid #e5e7eb;
                padding-top: 16px;
                margin-top: 8px;
                font-size: 16px;
                font-weight: 700;
            }

            .price-amount {
                color: $color-primary;
                font-size: inherit;
                font-weight: inherit;
            }
        }

        .mini-cart__buttons {
            margin: 0;
            gap: 12px;
            border-top: none !important;
            background: none !important;

            &.row {
                border: none !important;
                background: none !important;
                margin: 0 !important;
            }

            .btn {
                line-height: 1.5;
                font-weight: 600;
                text-align: center;
                padding: 12px 20px;
                color: #374151;
                border-radius: 8px;
                border: 1px solid #d1d5db;
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                width: 100%;
                font-size: 14px;
                white-space: nowrap;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                background-color: #ffffff;

                &:hover {
                    background-color: #f9fafb;
                    border-color: #9ca3af;
                    color: #111827;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                }

                &.checkout {
                    color: #fff;
                    background-color: $color-primary;
                    border-color: $color-primary;

                    &:hover {
                        background-color: darken($color-primary, 8%);
                        border-color: darken($color-primary, 8%);
                        color: #fff;
                        transform: translateY(-1px);
                        box-shadow: 0 6px 12px -2px rgba($color-primary, 0.3);
                    }
                }
            }
        }
    }
}

@media (min-width: 1200px) {
    .cart--mini {
        &:hover, &:focus {
            .cart__content {
                @include transform(translate(0, 0px));
                visibility: visible;
                opacity: 1;
            }
        }

        .cart__content {
            display: block;

            .panel__header {
                display: none;
            }
        }

        .cart__items {
            position: relative;

            &:before {
                content: '';
                display: inline-block;
                position: absolute;
                top: -8px;
                right: 30px;
                width: 16px;
                height: 16px;
                border-left: 1px solid #f3f4f6;
                border-top: 1px solid #f3f4f6;
                background-color: #ffffff;
                @include transform(rotate(45deg));
                @include transform-origin(50% 50%);
                box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.05);
            }

            .product--cart-mobile {
                margin-bottom: 30px;
            }
        }
    }
}

@media (max-width: 1199px) {
    .cart--mini {
        .cart__content {
            transform: none;
            transition: none;
            right: 0;
            padding-top: 0;
            visibility: visible;
            opacity: 1;
            display: block;

            &.active {
                position: fixed;
                top: 0;
                width: 100%;
                height: 100dvh;
                overflow-y: auto;
                z-index: 10001;
                left: auto;

                .mini-cart-content {
                    transform: translateX(0);
                    visibility: visible;
                    opacity: 1;
                }
            }

            .backdrop {
                position: absolute;
                top: 0;
                bottom: 0;
                height: 100%;
                width: 100%;
                background: #9d97975e;
            }

            .mini-cart-content {
                position: fixed;
                top: 0;
                width: 85%;
                max-width: 420px;
                height: 100dvh;
                overflow-y: auto;
                z-index: 10001;
                background-color: #fff;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0s;
                left: auto;
                right: 0;
                transform: translateX(100%);
                padding-top: 0;
                box-shadow: -10px 0 25px -5px rgba(0, 0, 0, 0.1);

                .widget-shopping-cart-content {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    height: 100%;
                    border-radius: 0;

                    .cart__items {
                        height: 100%;
                        overflow-y: auto;

                        .panel__header {
                            padding: 20px 24px;
                            border-bottom: 1px solid #f3f4f6;
                            background-color: #fafafa !important;
                            position: relative;
                            display: flex !important;
                            justify-content: space-between !important;
                            align-items: center !important;

                            h3 {
                                font-size: 18px;
                                font-weight: 600;
                                color: #111827 !important;
                                margin: 0;
                                flex: 1;
                                text-align: left;

                                .cart-counter {
                                    color: #6b7280;
                                    font-weight: 500;
                                }
                            }

                            .close-toggle--sidebar {
                                position: static !important;
                                right: auto !important;
                                top: auto !important;
                                transform: none !important;
                                width: 32px;
                                height: 32px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 6px;
                                background-color: #f3f4f6;
                                transition: all 0.2s ease;
                                flex-shrink: 0;
                                color: #6b7280 !important;
                                font-size: 16px !important;
                                padding: 0 !important;

                                &:hover {
                                    background-color: #e5e7eb;
                                    color: #374151 !important;
                                }

                                svg {
                                    width: 16px;
                                    height: 16px;
                                    color: inherit;
                                }
                            }
                        }
                    }
                }

                // Loading state for cart updates
                &.cart-updating {
                    position: relative;

                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background-color: rgba(255, 255, 255, 0.8);
                        z-index: 1000;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    &::after {
                        content: 'Updating cart...';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        z-index: 1001;
                        background-color: #fff;
                        padding: 12px 20px;
                        border-radius: 8px;
                        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                        font-size: 14px;
                        font-weight: 500;
                        color: #374151;
                    }
                }
            }
        }
    }
}

/* BULLETPROOF CART STYLING - OVERRIDES EVERYTHING */
.mini-cart-content {
    .cart-buttons-only,
    .control-buttons {
        padding: 20px 24px 24px !important;
        border: none !important;
        background: none !important;
        background-color: transparent !important;
        margin: 0 !important;

        .mini-cart__buttons.row {
            margin: 0 !important;
            border: none !important;
            background: none !important;
            background-color: transparent !important;
        }
    }

    .panel__header {
        padding: 20px 24px !important;
        border-bottom: 1px solid #f3f4f6 !important;
        background-color: #fafafa !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;

        h3 {
            font-size: 18px !important;
            font-weight: 600 !important;
            color: #111827 !important;
            margin: 0 !important;
            flex: 1 !important;
            text-align: left !important;
        }

        .close-toggle--sidebar {
            position: static !important;
            width: 32px !important;
            height: 32px !important;
            border-radius: 6px !important;
            background-color: #f3f4f6 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: #6b7280 !important;
        }
    }
}

.cart-form__contents {
    > :not(:first-child) {
        border-top-width: 0;
    }

    tbody {
        td {
            vertical-align: top;
            padding: 20px 10px;

            &.product-thumbnail {
                width: 90px;
            }

            .product-button .quantity {
                margin-bottom: 0px;
            }

            &.product-quantity {
                min-width: 120px;
            }
        }
    }

    thead {
        th {
            background-color: #f5f5f5;
            font-weight: 400;
            color: #555;
            padding: 11px 10px;
        }

        tr {
            border-top: 1px solid #eeeeee;
        }
    }

    tbody, thead {
        tr {
            border-bottom: 1px solid #eeeeee;
        }

        td, th {
            border-bottom-width: 0;

            &:first-child {
                border-left-width: 1px;
            }

            &:last-child {
                border-right-width: 1px;
            }
        }
    }
}

@media (max-width: 992px) {
    .cart-form__contents {
        tbody {
            td {
                padding: 10px 10px;
            }
        }
    }
}

@media (max-width: 767px) {
    .cart-form__contents {
        tbody {
            td {
                padding: 10px 7px;

                &.product-md {
                    border-top-width: 0;
                    padding-top: 0px;
                }

                &.product-subtotal {
                    .box-price {
                        .amount {
                            color: $color-green;
                        }
                    }
                }
            }
        }
    }
}

.wishlist-page-content {
    .cart-form__contents {
        .cart_item {
            .product-button {
                .quantity {
                    display: none;
                }
            }
        }
    }
}
