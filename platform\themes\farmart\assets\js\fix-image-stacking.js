// Immediate fix for image stacking - runs as soon as script loads
(function() {
    'use strict';
    
    // Function to hide stacked images
    function hideStackedImages() {
        const selectors = [
            '.bb-product-gallery-images',
            '.bb-quick-view-gallery-images',
            '.product-gallery__wrapper',
            '.slick-slides-carousel',
            '.product-deals-day__body',
            '.featured-brands__body',
            '.product-categories-body'
        ];
        
        selectors.forEach(function(selector) {
            const containers = document.querySelectorAll(selector);
            containers.forEach(function(container) {
                if (!container.classList.contains('slick-initialized')) {
                    const items = container.querySelectorAll('a, .slick-slide, .product-inner, .category-item, .brand-item');
                    items.forEach(function(item, index) {
                        if (index > 0) {
                            item.style.display = 'none';
                            item.setAttribute('data-hidden-by-fix', 'true');
                        }
                    });
                }
            });
        });
    }
    
    // Function to show images when slick is initialized
    function showImagesOnInit() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.classList.contains('slick-initialized')) {
                        const hiddenItems = target.querySelectorAll('[data-hidden-by-fix="true"]');
                        hiddenItems.forEach(function(item) {
                            item.style.display = '';
                            item.removeAttribute('data-hidden-by-fix');
                        });
                    }
                }
            });
        });
        
        // Observe all potential carousel containers
        const containers = document.querySelectorAll('.bb-product-gallery-images, .bb-quick-view-gallery-images, .product-gallery__wrapper, .slick-slides-carousel, .product-deals-day__body, .featured-brands__body, .product-categories-body');
        containers.forEach(function(container) {
            observer.observe(container, {
                attributes: true,
                attributeFilter: ['class']
            });
        });
    }
    
    // Run immediately
    hideStackedImages();
    showImagesOnInit();
    
    // Run on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            hideStackedImages();
            showImagesOnInit();
        });
    }
    
    // Run on window load
    window.addEventListener('load', function() {
        setTimeout(function() {
            hideStackedImages();
            showImagesOnInit();
        }, 100);
    });
    
    // Monitor for new content being added (AJAX, etc.)
    const bodyObserver = new MutationObserver(function(mutations) {
        let shouldRun = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const hasCarousel = node.querySelector && (
                            node.querySelector('.bb-product-gallery-images') ||
                            node.querySelector('.bb-quick-view-gallery-images') ||
                            node.querySelector('.slick-slides-carousel') ||
                            node.classList.contains('bb-product-gallery-images') ||
                            node.classList.contains('bb-quick-view-gallery-images') ||
                            node.classList.contains('slick-slides-carousel')
                        );
                        if (hasCarousel) {
                            shouldRun = true;
                        }
                    }
                });
            }
        });
        
        if (shouldRun) {
            setTimeout(function() {
                hideStackedImages();
                showImagesOnInit();
            }, 50);
        }
    });
    
    // Start observing the body for changes
    if (document.body) {
        bodyObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            bodyObserver.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    }
})();
