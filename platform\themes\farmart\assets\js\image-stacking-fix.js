/**
 * Simple Image Stacking Fix
 * Prevents images from stacking by hiding them immediately and showing only after slick init
 */

(function($) {
    'use strict';

    // Hide images immediately on page load
    function hideStackedImages() {
        // Hide all gallery images except first
        $('.bb-product-gallery-images > a:not(:first-child)').hide();
        $('.bb-quick-view-gallery-images > a:not(:first-child)').hide();
        
        // Show first image
        $('.bb-product-gallery-images > a:first-child').show();
        $('.bb-quick-view-gallery-images > a:first-child').show();
    }

    // Show all images after slick initialization
    function showAllImages() {
        $('.bb-product-gallery-images > a').show();
        $('.bb-quick-view-gallery-images > a').show();
    }

    // Override the original EcommerceApp.initProductGallery method
    function overrideInitProductGallery() {
        if (typeof window.EcommerceApp !== 'undefined' && window.EcommerceApp.initProductGallery) {
            const originalInitProductGallery = window.EcommerceApp.initProductGallery;
            
            window.EcommerceApp.initProductGallery = function(onlyQuickView = false) {
                // Hide images before initialization
                hideStackedImages();
                
                // Call original method
                const result = originalInitProductGallery.call(this, onlyQuickView);
                
                // Show all images after a short delay to ensure slick is initialized
                setTimeout(function() {
                    showAllImages();
                }, 200);
                
                return result;
            };
        }
    }

    // Initialize immediately
    hideStackedImages();

    // Override when DOM is ready
    $(document).ready(function() {
        hideStackedImages();
        overrideInitProductGallery();
    });

    // Handle AJAX content
    $(document).ajaxComplete(function() {
        setTimeout(function() {
            hideStackedImages();
        }, 50);
    });

    // Handle quick view modal
    $(document).on('shown.bs.modal', '.bb-quick-view-modal', function() {
        hideStackedImages();
        setTimeout(function() {
            if (typeof window.EcommerceApp !== 'undefined') {
                window.EcommerceApp.initProductGallery(true);
            }
        }, 100);
    });

})(jQuery);

// Also add immediate CSS to prevent any flash
const style = document.createElement('style');
style.textContent = `
    .bb-product-gallery-images > a:not(:first-child),
    .bb-quick-view-gallery-images > a:not(:first-child) {
        display: none !important;
    }
`;
document.head.appendChild(style);
