// Modal close button fix
(function() {
    // Function to enhance the existing close button
    function enhanceCloseButton() {
        // Find the quick view modal
        const modal = document.getElementById('product-quick-view-modal');
        if (!modal) return;

        // Find the existing close button
        const closeButton = modal.querySelector('.btn-close');
        if (!closeButton) return;

        // Enhance the existing button
        closeButton.style.zIndex = '99999';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '20px';
        closeButton.style.right = '20px';
        closeButton.style.opacity = '1';
        closeButton.style.backgroundColor = 'white';
        closeButton.style.border = '1px solid #ccc';
        closeButton.style.borderRadius = '50%';
        closeButton.style.width = '40px';
        closeButton.style.height = '40px';
        closeButton.style.display = 'flex';
        closeButton.style.alignItems = 'center';
        closeButton.style.justifyContent = 'center';
        closeButton.style.cursor = 'pointer';
        closeButton.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';

        // Add enhanced click event to close the modal
        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Try multiple methods to close the modal
            // Method 1: Using Bootstrap API
            if (typeof bootstrap !== 'undefined') {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            }

            // Method 2: Using jQuery
            if (typeof jQuery !== 'undefined') {
                jQuery(modal).modal('hide');
            }

            // Method 3: Direct DOM manipulation
            modal.classList.remove('show');
            modal.style.display = 'none';
            document.body.classList.remove('modal-open');

            // Remove backdrop
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(function(backdrop) {
                backdrop.remove();
            });
        });
    }

    // Function to handle modal open event
    function handleModalOpen() {
        // Check if modal is visible
        const modal = document.getElementById('product-quick-view-modal');
        if (modal && (modal.classList.contains('show') || getComputedStyle(modal).display !== 'none')) {
            enhanceCloseButton();
        }
    }

    // Watch for modal to open
    function watchForModalOpen() {
        // Method 1: MutationObserver to watch for changes to the modal
        const modal = document.getElementById('product-quick-view-modal');
        if (modal) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.attributeName === 'class' && modal.classList.contains('show')) {
                        enhanceCloseButton();
                    }
                });
            });

            observer.observe(modal, { attributes: true });
        }

        // Method 2: Watch for clicks on quick view buttons
        document.addEventListener('click', function(e) {
            if (e.target.closest('.quick-view')) {
                // Wait for modal to open
                setTimeout(handleModalOpen, 500);
                setTimeout(handleModalOpen, 1000);
            }
        });

        // Method 3: Listen for Bootstrap modal events
        if (typeof jQuery !== 'undefined') {
            jQuery(document).on('shown.bs.modal', '#product-quick-view-modal', function() {
                enhanceCloseButton();
            });
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', watchForModalOpen);
    } else {
        watchForModalOpen();
    }

    // Also listen for the custom event
    document.addEventListener('ecommerce.quick-view.initialized', function() {
        setTimeout(enhanceCloseButton, 100);
    });
})();
