// Fix for sticky bars positioning
document.addEventListener('DOMContentLoaded', function() {
    // Function to fix the positioning of sticky bars
    function fixStickyBars() {
        if (window.innerWidth <= 991) {
            // Get the footer mobile bar height
            const footerMobile = document.querySelector('.footer-mobile');
            if (!footerMobile) return;

            // Set a fixed height for the footer mobile bar
            const footerHeight = 60; // Fixed height in pixels
            footerMobile.style.height = footerHeight + 'px';

            // Position the add-to-cart sticky bar above the footer mobile bar
            const stickyBar = document.querySelector('#sticky-add-to-cart .sticky-atc-wrap');
            if (stickyBar) {
                stickyBar.style.bottom = footerHeight + 'px';

                // Make the sticky bar more compact
                const quantitySection = stickyBar.querySelector('.quantity');
                if (quantitySection) {
                    // Make the quantity section horizontal
                    quantitySection.style.display = 'flex';
                    quantitySection.style.alignItems = 'center';
                    quantitySection.style.marginBottom = '0';
                    quantitySection.style.marginRight = '10px';
                }

                // Style the buttons
                const buttons = stickyBar.querySelectorAll('.btn');
                buttons.forEach(function(btn) {
                    btn.style.flex = '1';
                    btn.style.margin = '0 5px';
                    btn.style.padding = '6px 5px';
                    btn.style.fontSize = '13px';
                    btn.style.whiteSpace = 'nowrap';
                });
            }

            // Style the footer mobile bar
            const menuFooter = footerMobile.querySelector('.menu--footer');
            if (menuFooter) {
                menuFooter.style.margin = '0';
                menuFooter.style.padding = '0';
                menuFooter.style.display = 'flex';
                menuFooter.style.justifyContent = 'space-around';
                menuFooter.style.alignItems = 'center';
                menuFooter.style.height = '100%';

                // Style the menu items
                const menuItems = menuFooter.querySelectorAll('li');
                menuItems.forEach(function(item) {
                    item.style.flex = '1';
                    item.style.textAlign = 'center';
                    item.style.height = '100%';
                    item.style.display = 'flex';
                    item.style.flexDirection = 'column';
                    item.style.justifyContent = 'center';
                    item.style.padding = '5px 0';

                    // Style the links
                    const link = item.querySelector('a');
                    if (link) {
                        link.style.display = 'flex';
                        link.style.flexDirection = 'column';
                        link.style.alignItems = 'center';
                        link.style.justifyContent = 'center';
                        link.style.height = '100%';
                    }

                    // Style the icons
                    const icon = item.querySelector('i');
                    if (icon) {
                        icon.style.fontSize = '18px';
                        icon.style.marginBottom = '2px';
                    }

                    // Style the text
                    const text = item.querySelector('span');
                    if (text) {
                        text.style.fontSize = '10px';
                        text.style.lineHeight = '1';
                    }
                });
            }

            // Add padding to the page to accommodate both bars
            const productContainer = document.querySelector('.single-product .product-detail-container');
            if (productContainer) {
                const stickyBarHeight = stickyBar ? stickyBar.offsetHeight : 0;
                const totalHeight = footerHeight + stickyBarHeight + 20; // Add some extra padding
                productContainer.style.paddingBottom = totalHeight + 'px';
            }
        }
    }

    // Run on page load
    fixStickyBars();

    // Run on window resize
    window.addEventListener('resize', fixStickyBars);

    // Run after short delays to ensure all elements are loaded
    setTimeout(fixStickyBars, 100);
    setTimeout(fixStickyBars, 500);
    setTimeout(fixStickyBars, 1000);

    // Run when images are loaded
    window.addEventListener('load', fixStickyBars);
});
