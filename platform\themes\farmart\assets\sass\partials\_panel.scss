%list-reset {
    ul, ol {
        margin: 0;
        padding: 0;
        list-style: none;
    }
}

.panel--sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 82%;
    max-width: 400px;
    height: 100vh;
    overflow-y: auto;
    z-index: 10001;
    background-color: #fff;
    @include transform(translateX(-101%));
    transition: all 0.5s cubic-bezier(0.7, 0, 0.3, 1) 0s;
    @extend %list-reset;

    &.panel--sidebar__right {
        left: auto;
        right: 0;
        transform: translateX(101%);
    }

    .panel__content {
        padding-top: 10px;
        padding-bottom: 70px;
    }

    &.active {
        @include transform(translateX(0));

        ~ .panel-overlay-layer {
            display: block;
        }
    }
}

.panel__header {
    position: relative;
    text-align: center;
    background-color: $color-primary;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 23px 20px 23px;

    .panel__header-title {
        font-weight: 700;
    }

    .close-toggle--sidebar {
        color: #000;
        font-size: 24px;
        padding: 0 0 0 3px;
    }

    h3 {
        margin-bottom: 0;
    }
}

.side-left {
    .panel__header {
        .panel__header-title {
            order: 1;
        }

        .close-toggle--sidebar {
            transform: rotateY(180deg);
        }
    }
}

.panel-overlay-layer {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0, .25);
    display: none;
}
